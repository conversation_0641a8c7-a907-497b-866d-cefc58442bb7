# Ergo Raffle V2 Frontend & API

This document describes the newly implemented frontend and API service for the Ergo Raffle V2 system.

## 🏗️ Architecture

### Backend API Service (`services/api/`)
- **Express.js** REST API server
- **TypeORM** database integration with existing entities
- **CORS** enabled for frontend communication
- **Helmet** for security headers
- **Compression** for response optimization

### Frontend Application (`frontend/`)
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Bootstrap 5** for responsive UI components
- **React Query** for efficient data fetching and caching
- **React Router** for client-side routing

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ (some dependencies require Node 20+, but will work with warnings)
- npm or yarn package manager

### Installation

1. **Install root dependencies:**
   ```bash
   npm install
   ```

2. **Install API service dependencies:**
   ```bash
   cd services/api
   npm install
   ```

3. **Install frontend dependencies:**
   ```bash
   cd frontend
   npm install
   ```

### Running the Applications

#### 1. Start the Background Job Service (Database Scanner)
```bash
cd services/background-job
npm run dev
```
This service scans the blockchain and populates the database with raffle data.

#### 2. Start the API Service
```bash
cd services/api
npm run dev
```
The API will be available at `http://localhost:3001`

#### 3. Start the Frontend
```bash
cd frontend
npm run dev
```
The frontend will be available at `http://localhost:3000`

## 📡 API Endpoints

### Raffles
- `GET /api/raffles` - List all raffles (with pagination)
- `GET /api/raffles/:id` - Get raffle details
- `GET /api/raffles/:id/tickets` - Get raffle tickets
- `GET /api/raffles/:id/gifts` - Get raffle gifts
- `GET /api/raffles/:id/winners` - Get raffle winners

### Health Check
- `GET /health` - API health status

## 🎨 Frontend Features

### Pages
- **Home** - Browse active and completed raffles
- **Raffle Details** - View detailed raffle information, tickets, gifts, and winners
- **Create Raffle** - Form to create new raffles (UI only, backend integration pending)
- **My Tickets** - User's purchased tickets (placeholder)
- **Winners** - Winners showcase (placeholder)

### Components
- **RaffleCard** - Displays raffle summary with progress, status, and key metrics
- **Header** - Navigation with wallet connection (placeholder)
- **Footer** - Site information and links
- **LoadingSpinner** - Consistent loading states
- **ErrorMessage** - Error handling with retry functionality

### Key Features
- **Responsive Design** - Works on desktop, tablet, and mobile
- **Real-time Data** - React Query provides efficient data fetching and caching
- **Progress Tracking** - Visual progress bars for raffle funding goals
- **Status Badges** - Clear visual indicators for raffle states
- **Pagination** - Efficient browsing of large raffle lists
- **Error Handling** - Graceful error states with retry options

## 🔧 Configuration

### API Configuration (`services/api/config/default.yml`)
```yaml
server:
  port: 3001
  host: '0.0.0.0'

cors:
  origin: 'http://localhost:3000'
  credentials: true

database:
  type: 'sqlite'
  path: '../background-job/job.sqlite'
```

### Frontend Configuration
- **Vite Proxy** - API calls are proxied to `http://localhost:3001`
- **Environment Variables** - `VITE_API_BASE_URL` for API endpoint configuration

## 🎯 Next Steps

### Immediate Priorities
1. **Wallet Integration** - Connect Ergo wallet for transactions
2. **Transaction Building** - Implement raffle creation and ticket purchasing
3. **Real-time Updates** - WebSocket or polling for live raffle updates
4. **Image Upload** - Support for raffle images and NFT displays

### Future Enhancements
1. **User Authentication** - Wallet-based authentication system
2. **Advanced Filtering** - Search and filter raffles by various criteria
3. **Analytics Dashboard** - Statistics and insights for raffle creators
4. **Mobile App** - React Native implementation
5. **Internationalization** - Multi-language support

## 🧪 Testing

### API Testing
```bash
cd services/api
npm test
```

### Frontend Testing
```bash
cd frontend
npm test
```

### Manual Testing
1. Start all services as described above
2. Navigate to `http://localhost:3000`
3. Browse raffles and test navigation
4. Check API responses at `http://localhost:3001/api/raffles`

## 📁 Project Structure

```
├── services/
│   └── api/                 # REST API service
│       ├── src/
│       │   ├── config/      # Configuration files
│       │   ├── routes/      # API route handlers
│       │   ├── services/    # Business logic
│       │   └── types/       # TypeScript type definitions
│       └── config/          # YAML configuration files
├── frontend/                # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/           # Page components
│   │   ├── hooks/           # Custom React hooks
│   │   ├── services/        # API service layer
│   │   ├── types/           # TypeScript type definitions
│   │   ├── utils/           # Utility functions
│   │   └── styles/          # Custom CSS styles
│   └── public/              # Static assets
└── README.md               # This file
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add TypeScript types for all new code
3. Include error handling and loading states
4. Test thoroughly before submitting changes
5. Update documentation as needed

## 📄 License

This project is part of the Ergo Raffle V2 system and follows the same licensing terms as the main project.
