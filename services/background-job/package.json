{"name": "@ergo-raffle/background-job", "version": "0.1.0", "description": "", "repository": "", "license": "GPL-3.0", "author": "Ergo Raffle Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"coverage": "npm run test -- --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "start": "tsx watch ./src/index.ts", "start:prod": "tsx ./dist/index.js", "test": "NODE_OPTIONS='--import tsx' vitest", "type-check": "tsc --noEmit", "typeorm": "NODE_OPTIONS='--import tsx' typeorm", "typeorm:generate:postgres": "npm run typeorm migration:generate ./src/migrations/postgres/postgres -- -p -d ./src/dataSource.ts", "typeorm:generate:sqlite": "npm run typeorm migration:generate ./src/migrations/sqlite/sqlite -- -p -d ./src/dataSource.ts", "typeorm:migrate:postgres": "npm run typeorm migration:run -- -d ./src/dataSource.ts", "typeorm:migrate:sqlite": "npm run typeorm migration:run -- -d ./src/dataSource.ts"}, "dependencies": {"@ergo-raffle/contracts": "^0.1.0", "@ergo-raffle/extractors": "^0.1.0", "@rosen-bridge/callback-logger": "^0.1.0", "@rosen-bridge/extended-typeorm": "^0.0.3", "@rosen-bridge/scanner": "^6.0.0-8792333e", "@rosen-bridge/scanner-interfaces": "^0.1.0-8792333e", "@rosen-bridge/service-manager": "^0.1.2", "@rosen-bridge/winston-logger": "^1.0.3", "config": "^3.3.12", "lodash-es": "^4.17.21", "reflect-metadata": "^0.1.14", "sqlite3": "^5.1.7", "typeorm": "0.3.20"}, "devDependencies": {"@types/config": "^3.3.5", "@types/node": "^20.11.9", "@types/lodash-es": "^4.17.12", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "tsconfig-paths": "^4.1.2", "tsx": "^4.19.3", "typescript": "^5.3.3", "vitest": "^3.1.1"}}