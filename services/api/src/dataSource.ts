import { DataSource } from 'typeorm';
import { getConfig } from './config/config.js';
import * as path from 'path';

// Import entities from the background job service
// We'll create simplified entities that match the database schema

import {
  RaffleServiceEntity,
  RaffleEntity,
  RaffleDetailsEntity,
  PictureEntity,
  TicketEntity,
  GiftEntity,
  WinnerEntity,
} from './entities/index.js';

const dbConfigs = getConfig().database;

const commonConfigs = {
  entities: [
    RaffleServiceEntity,
    RaffleEntity,
    RaffleDetailsEntity,
    PictureEntity,
    TicketEntity,
    GiftEntity,
    WinnerEntity,
  ],
  synchronize: false,
  logging: false,
};

let dataSource: DataSource;
if (dbConfigs.type === 'sqlite') {
  dataSource = new DataSource({
    type: 'sqlite',
    database: dbConfigs.path!,
    ...commonConfigs,
  });
} else {
  dataSource = new DataSource({
    type: 'postgres',
    host: dbConfigs.host!,
    port: dbConfigs.port!,
    username: dbConfigs.user!,
    password: dbConfigs.password!,
    database: dbConfigs.name!,
    ...commonConfigs,
  });
}

export default dataSource;
