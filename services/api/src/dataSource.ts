import { DataSource } from 'typeorm';
import { getConfig } from './config/config.js';

// For now, we'll create simple entity interfaces until the extractors package is properly integrated
// These will be replaced with actual entities from @ergo-raffle/extractors once the build issues are resolved

const dbConfigs = getConfig().database;

const commonConfigs = {
  entities: [], // Will be populated when extractors package is properly integrated
  synchronize: false,
  logging: false,
};

let dataSource: DataSource;
if (dbConfigs.type === 'sqlite') {
  dataSource = new DataSource({
    type: 'sqlite',
    database: dbConfigs.path!,
    ...commonConfigs,
  });
} else {
  dataSource = new DataSource({
    type: 'postgres',
    host: dbConfigs.host!,
    port: dbConfigs.port!,
    username: dbConfigs.user!,
    password: dbConfigs.password!,
    database: dbConfigs.name!,
    ...commonConfigs,
  });
}

export default dataSource;
