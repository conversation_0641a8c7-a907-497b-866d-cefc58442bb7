import config from 'config';
import { TransportOptions } from '@rosen-bridge/winston-logger';

interface ServerConfig {
  port: number;
  host: string;
}

interface CorsConfig {
  origin: string;
  credentials: boolean;
}

interface DatabaseConfig {
  type: 'sqlite' | 'postgres';
  path?: string;
  host?: string;
  port?: number;
  user?: string;
  password?: string;
  name?: string;
}

interface ConfigType {
  server: ServerConfig;
  cors: CorsConfig;
  database: DatabaseConfig;
  logs: TransportOptions[];
}

let internalConfig: ConfigType | undefined;

const getConfig = (): ConfigType => {
  if (internalConfig == undefined) {
    internalConfig = {
      server: config.get<ServerConfig>('server'),
      cors: config.get<CorsConfig>('cors'),
      database: config.get<DatabaseConfig>('database'),
      logs: config.get<TransportOptions[]>('logs'),
    };
  }
  return internalConfig;
};

export { getConfig, ConfigType, ServerConfig, CorsConfig, DatabaseConfig };
