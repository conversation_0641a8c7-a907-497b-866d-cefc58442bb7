import 'reflect-metadata';
import { getConfig } from './config/config.js';
import dataSource from './dataSource.js';
import app from './app.js';

const config = getConfig();

// Simple console logger for now
const logger = {
  info: (message: string, ...args: any[]) => console.log(`[INFO] ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`[ERROR] ${message}`, ...args),
  debug: (message: string, ...args: any[]) => console.debug(`[DEBUG] ${message}`, ...args),
};

async function startServer() {
  try {
    // Initialize database connection
    logger.info('Initializing database connection...');
    // Skip database initialization for now since we're using mock data
    // await dataSource.initialize();
    logger.info('Using mock data - database connection skipped');

    // Start the server
    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info(`API server running on http://${config.server.host}:${config.server.port}`);
      logger.info('Available endpoints:');
      logger.info('  GET /health - Health check');
      logger.info('  GET /api/raffles - List all raffles');
      logger.info('  GET /api/raffles/:id - Get raffle details');
      logger.info('  GET /api/raffles/:id/tickets - Get raffle tickets');
      logger.info('  GET /api/raffles/:id/gifts - Get raffle gifts');
      logger.info('  GET /api/raffles/:id/winners - Get raffle winners');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      logger.info('SIGTERM received, shutting down gracefully');
      server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
      });
    });

    process.on('SIGINT', () => {
      logger.info('SIGINT received, shutting down gracefully');
      server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
      });
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

startServer();
