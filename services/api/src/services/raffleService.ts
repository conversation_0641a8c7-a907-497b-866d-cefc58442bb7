import { Repository } from 'typeorm';
import dataSource from '../dataSource.js';
import { RaffleResponse, TicketResponse, GiftResponse, WinnerResponse, PaginatedResponse } from '../types/api.js';
import {
  RaffleEntity,
  RaffleDetailsEntity,
  PictureEntity,
  TicketEntity,
  GiftEntity,
  WinnerEntity,
} from '../entities/index.js';

export class RaffleService {
  private raffleRepo: Repository<RaffleEntity>;
  private raffleDetailsRepo: Repository<RaffleDetailsEntity>;
  private pictureRepo: Repository<PictureEntity>;
  private ticketRepo: Repository<TicketEntity>;
  private giftRepo: Repository<GiftEntity>;
  private winnerRepo: Repository<WinnerEntity>;

  constructor() {
    this.raffleRepo = dataSource.getRepository(RaffleEntity);
    this.raffleDetailsRepo = dataSource.getRepository(RaffleDetailsEntity);
    this.pictureRepo = dataSource.getRepository(PictureEntity);
    this.ticketRepo = dataSource.getRepository(TicketEntity);
    this.giftRepo = dataSource.getRepository(GiftEntity);
    this.winnerRepo = dataSource.getRepository(WinnerEntity);
  }

  async getAllRaffles(page = 1, limit = 10): Promise<PaginatedResponse<RaffleResponse>> {
    try {
      const [raffles, total] = await this.raffleRepo.findAndCount({
        skip: (page - 1) * limit,
        take: limit,
        order: { createdAt: 'DESC' },
      });

      const raffleResponses = await Promise.all(
        raffles.map(raffle => this.mapRaffleToResponse(raffle))
      );

      return {
        items: raffleResponses,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      console.error('Error fetching raffles from database:', error);
      // Fallback to mock data if database is not available
      return this.getMockRaffles(page, limit);
    }
  }

  private getMockRaffles(page = 1, limit = 10): PaginatedResponse<RaffleResponse> {
    const mockRaffles: RaffleResponse[] = [
      {
        id: 'raffle-1',
        name: 'Sample Raffle 1',
        description: 'This is a sample raffle for demonstration purposes.',
        creatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pD',
        ticketPrice: '100000000', // 0.1 ERG in nanoERG
        goal: '10000000000', // 10 ERG in nanoERG
        deadline: Math.floor(Date.now() / 1000) + 86400 * 7, // 7 days from now
        winnersCount: 3,
        winnersPercent: 80,
        winnersPercentList: [50, 30, 20],
        serviceFeePercent: 5,
        implementerFeePercent: 5,
        status: 'active' as const,
        pictures: [
          { orderIndex: 0, content: 'https://via.placeholder.com/400x300?text=Raffle+1' }
        ],
        totalSoldTickets: '25',
        totalGifts: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'raffle-2',
        name: 'Sample Raffle 2',
        description: 'Another sample raffle with different parameters.',
        creatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pE',
        ticketPrice: '50000000', // 0.05 ERG in nanoERG
        goal: '5000000000', // 5 ERG in nanoERG
        deadline: Math.floor(Date.now() / 1000) + 86400 * 3, // 3 days from now
        winnersCount: 1,
        winnersPercent: 90,
        winnersPercentList: [100],
        serviceFeePercent: 5,
        implementerFeePercent: 5,
        status: 'active' as const,
        pictures: [
          { orderIndex: 0, content: 'https://via.placeholder.com/400x300?text=Raffle+2' }
        ],
        totalSoldTickets: '80',
        totalGifts: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRaffles = mockRaffles.slice(startIndex, endIndex);

    return {
      items: paginatedRaffles,
      total: mockRaffles.length,
      page,
      limit,
      totalPages: Math.ceil(mockRaffles.length / limit),
    };
  }

  async getRaffleById(raffleId: string): Promise<RaffleResponse | null> {
    try {
      const raffle = await this.raffleRepo.findOne({
        where: { raffleId },
      });

      if (!raffle) {
        return null;
      }

      return this.mapRaffleToResponse(raffle);
    } catch (error) {
      console.error('Error fetching raffle from database:', error);
      // Fallback to mock data
      const allRaffles = this.getMockRaffles(1, 100);
      return allRaffles.items.find(raffle => raffle.id === raffleId) || null;
    }
  }

  async getRaffleTickets(raffleId: string): Promise<TicketResponse[]> {
    try {
      const tickets = await this.ticketRepo.find({
        where: { raffleId },
        order: { createdAt: 'DESC' },
      });

      return tickets.map(ticket => ({
        id: ticket.id.toString(),
        raffleId: ticket.raffleId,
        donatorErgoTree: ticket.donatorErgoTree,
        rangeStart: ticket.rangeStart,
        rangeEnd: ticket.rangeEnd,
        txId: ticket.txId,
        createdAt: ticket.createdAt.toISOString(),
      }));
    } catch (error) {
      console.error('Error fetching tickets from database:', error);
      // Return mock data as fallback
      return [
        {
          id: 'ticket-1',
          raffleId,
          donatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pF',
          rangeStart: '1',
          rangeEnd: '5',
          txId: 'tx123456789',
          createdAt: new Date().toISOString(),
        }
      ];
    }
  }

  async getRaffleGifts(raffleId: string): Promise<GiftResponse[]> {
    try {
      const gifts = await this.giftRepo.find({
        where: { raffleId },
        order: { createdAt: 'DESC' },
      });

      return gifts.map(gift => ({
        id: gift.id.toString(),
        raffleId: gift.raffleId,
        donatorErgoTree: gift.donatorErgoTree,
        winnerIndex: gift.winnerIndex,
        txId: gift.txId,
        createdAt: gift.createdAt.toISOString(),
      }));
    } catch (error) {
      console.error('Error fetching gifts from database:', error);
      return [];
    }
  }

  async getRaffleWinners(raffleId: string): Promise<WinnerResponse[]> {
    try {
      const winners = await this.winnerRepo.find({
        where: { raffleId },
        order: { index: 'ASC' },
      });

      return winners.map(winner => ({
        id: winner.id.toString(),
        raffleId: winner.raffleId,
        index: winner.index,
        rewardPercent: winner.rewardPercent,
        txId: winner.txId,
        createdAt: winner.createdAt.toISOString(),
      }));
    } catch (error) {
      console.error('Error fetching winners from database:', error);
      return [];
    }
  }

  private async mapRaffleToResponse(raffle: RaffleEntity): Promise<RaffleResponse> {
    try {
      // Get raffle details
      const details = await this.raffleDetailsRepo.findOne({
        where: { raffleId: raffle.raffleId },
      });

      // Get pictures
      const pictures = await this.pictureRepo.find({
        where: { raffleId: raffle.raffleId },
        order: { orderIndex: 'ASC' },
      });

      // Get ticket count
      const ticketCount = await this.ticketRepo.count({
        where: { raffleId: raffle.raffleId },
      });

      // Get gift count
      const giftCount = await this.giftRepo.count({
        where: { raffleId: raffle.raffleId },
      });

      // Parse winners percent list
      let winnersPercentList: number[] = [];
      try {
        winnersPercentList = JSON.parse(raffle.winnersPercentList);
      } catch {
        winnersPercentList = [];
      }

      // Determine status based on current time and raffle state
      const now = Date.now();
      const deadlineMs = raffle.deadline * 1000;
      let status: 'inactive' | 'active' | 'success' | 'failed' = 'inactive';

      if (now < deadlineMs) {
        status = 'active';
      } else {
        // Check if raffle succeeded (this is simplified logic)
        status = 'failed'; // Would need to check actual success conditions
      }

      return {
        id: raffle.raffleId,
        name: details?.name || 'Unnamed Raffle',
        description: details?.description || '',
        creatorErgoTree: raffle.creatorErgoTree,
        ticketPrice: raffle.ticketPrice,
        goal: raffle.goal,
        deadline: raffle.deadline,
        winnersCount: winnersPercentList.length,
        winnersPercent: raffle.winnersPercent,
        winnersPercentList,
        serviceFeePercent: raffle.serviceFeePercent,
        implementerFeePercent: raffle.implementerFeePercent,
        status,
        pictures: pictures.map(pic => ({
          orderIndex: pic.orderIndex,
          content: pic.content,
        })),
        totalSoldTickets: ticketCount.toString(),
        totalGifts: giftCount,
        createdAt: raffle.createdAt.toISOString(),
        updatedAt: raffle.updatedAt.toISOString(),
      };
    } catch (error) {
      console.error('Error mapping raffle to response:', error);
      // Return basic response if mapping fails
      return {
        id: raffle.raffleId,
        name: 'Error Loading Raffle',
        description: 'Failed to load raffle details',
        creatorErgoTree: raffle.creatorErgoTree,
        ticketPrice: raffle.ticketPrice,
        goal: raffle.goal,
        deadline: raffle.deadline,
        winnersCount: 0,
        winnersPercent: raffle.winnersPercent,
        winnersPercentList: [],
        serviceFeePercent: raffle.serviceFeePercent,
        implementerFeePercent: raffle.implementerFeePercent,
        status: 'inactive' as const,
        pictures: [],
        totalSoldTickets: '0',
        totalGifts: 0,
        createdAt: raffle.createdAt.toISOString(),
        updatedAt: raffle.updatedAt.toISOString(),
      };
    }
  }
}
