import { Repository } from 'typeorm';
import dataSource from '../dataSource.js';
import { RaffleResponse, TicketResponse, GiftResponse, WinnerResponse, PaginatedResponse } from '../types/api.js';

// Temporary mock entities until extractors package is properly integrated
interface MockEntity {
  id: number;
  createdAt: Date;
  updatedAt: Date;
}

interface RaffleEntity extends MockEntity {
  raffleId: string;
  creatorErgoTree: string;
  ticketPrice: bigint;
  goal: bigint;
  deadline: number;
  winnersPercent: number;
  winnersPercentList: string;
  serviceFeePercent: number;
  implementerFeePercent: number;
}

interface RaffleDetailsEntity extends MockEntity {
  raffleId: string;
  name: string;
  description: string;
}

interface PictureEntity extends MockEntity {
  raffleId: string;
  orderIndex: number;
  content: string;
}

interface TicketEntity extends MockEntity {
  raffleId: string;
  donatorErgoTree: string;
  rangeStart: bigint;
  rangeEnd: bigint;
  txId: string;
}

interface GiftEntity extends MockEntity {
  raffleId: string;
  donatorErgoTree: string;
  winnerIndex: number;
  txId: string;
}

interface WinnerEntity extends MockEntity {
  raffleId: string;
  index: number;
  rewardPercent: number;
  txId: string;
}

export class RaffleService {
  constructor() {
    // Repositories will be initialized when extractors package is properly integrated
  }

  async getAllRaffles(page = 1, limit = 10): Promise<PaginatedResponse<RaffleResponse>> {
    // Return mock data until database integration is complete
    const mockRaffles: RaffleResponse[] = [
      {
        id: 'raffle-1',
        name: 'Sample Raffle 1',
        description: 'This is a sample raffle for demonstration purposes.',
        creatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pD',
        ticketPrice: '100000000', // 0.1 ERG in nanoERG
        goal: '10000000000', // 10 ERG in nanoERG
        deadline: Math.floor(Date.now() / 1000) + 86400 * 7, // 7 days from now
        winnersCount: 3,
        winnersPercent: 80,
        winnersPercentList: [50, 30, 20],
        serviceFeePercent: 5,
        implementerFeePercent: 5,
        status: 'active' as const,
        pictures: [
          { orderIndex: 0, content: 'https://via.placeholder.com/400x300?text=Raffle+1' }
        ],
        totalSoldTickets: '25',
        totalGifts: 2,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'raffle-2',
        name: 'Sample Raffle 2',
        description: 'Another sample raffle with different parameters.',
        creatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pE',
        ticketPrice: '50000000', // 0.05 ERG in nanoERG
        goal: '5000000000', // 5 ERG in nanoERG
        deadline: Math.floor(Date.now() / 1000) + 86400 * 3, // 3 days from now
        winnersCount: 1,
        winnersPercent: 90,
        winnersPercentList: [100],
        serviceFeePercent: 5,
        implementerFeePercent: 5,
        status: 'active' as const,
        pictures: [
          { orderIndex: 0, content: 'https://via.placeholder.com/400x300?text=Raffle+2' }
        ],
        totalSoldTickets: '80',
        totalGifts: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    ];

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRaffles = mockRaffles.slice(startIndex, endIndex);

    return {
      items: paginatedRaffles,
      total: mockRaffles.length,
      page,
      limit,
      totalPages: Math.ceil(mockRaffles.length / limit),
    };
  }

  async getRaffleById(raffleId: string): Promise<RaffleResponse | null> {
    // Return mock data for the requested raffle
    const allRaffles = await this.getAllRaffles(1, 100);
    return allRaffles.items.find(raffle => raffle.id === raffleId) || null;
  }

  async getRaffleTickets(raffleId: string): Promise<TicketResponse[]> {
    // Return mock ticket data
    return [
      {
        id: 'ticket-1',
        raffleId,
        donatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pF',
        rangeStart: '1',
        rangeEnd: '5',
        txId: 'tx123456789',
        createdAt: new Date().toISOString(),
      },
      {
        id: 'ticket-2',
        raffleId,
        donatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pG',
        rangeStart: '6',
        rangeEnd: '10',
        txId: 'tx987654321',
        createdAt: new Date().toISOString(),
      }
    ];
  }

  async getRaffleGifts(raffleId: string): Promise<GiftResponse[]> {
    // Return mock gift data
    return [
      {
        id: 'gift-1',
        raffleId,
        donatorErgoTree: '9f4QF8AD1nQ3nJahQVeM8YhHZ9dNzKyWLBnXA7GCu2ZSf6pH',
        winnerIndex: 0,
        txId: 'giftTx123',
        createdAt: new Date().toISOString(),
      }
    ];
  }

  async getRaffleWinners(raffleId: string): Promise<WinnerResponse[]> {
    // Return empty array as winners are determined when raffle ends
    return [];
  }

  private async mapRaffleToResponse(raffle: RaffleEntity): Promise<RaffleResponse> {
    // Get raffle details
    const details = await this.raffleDetailsRepo.findOne({
      where: { raffleId: raffle.raffleId },
    });

    // Get pictures
    const pictures = await this.pictureRepo.find({
      where: { raffleId: raffle.raffleId },
      order: { orderIndex: 'ASC' },
    });

    // Get ticket count
    const ticketCount = await this.ticketRepo.count({
      where: { raffleId: raffle.raffleId },
    });

    // Get gift count
    const giftCount = await this.giftRepo.count({
      where: { raffleId: raffle.raffleId },
    });

    // Parse winners percent list
    let winnersPercentList: number[] = [];
    try {
      winnersPercentList = JSON.parse(raffle.winnersPercentList);
    } catch {
      winnersPercentList = [];
    }

    // Determine status based on current time and raffle state
    const now = Date.now();
    const deadlineMs = raffle.deadline * 1000;
    let status: 'inactive' | 'active' | 'success' | 'failed' = 'inactive';
    
    if (now < deadlineMs) {
      status = 'active';
    } else {
      // Check if raffle succeeded (this is simplified logic)
      status = 'failed'; // Would need to check actual success conditions
    }

    return {
      id: raffle.raffleId,
      name: details?.name || 'Unnamed Raffle',
      description: details?.description || '',
      creatorErgoTree: raffle.creatorErgoTree,
      ticketPrice: raffle.ticketPrice.toString(),
      goal: raffle.goal.toString(),
      deadline: raffle.deadline,
      winnersCount: winnersPercentList.length,
      winnersPercent: raffle.winnersPercent,
      winnersPercentList,
      serviceFeePercent: raffle.serviceFeePercent,
      implementerFeePercent: raffle.implementerFeePercent,
      status,
      pictures: pictures.map(pic => ({
        orderIndex: pic.orderIndex,
        content: pic.content,
      })),
      totalSoldTickets: ticketCount.toString(),
      totalGifts: giftCount,
      createdAt: raffle.createdAt.toISOString(),
      updatedAt: raffle.updatedAt.toISOString(),
    };
  }
}
