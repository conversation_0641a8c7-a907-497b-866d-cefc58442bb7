import { WebSocketServer } from 'ws';
import WebSocket from 'ws';
import { Server } from 'http';

interface WebSocketMessage {
  type: 'raffle_update' | 'ticket_purchased' | 'gift_added' | 'raffle_ended' | 'winner_selected';
  data: any;
  raffleId?: string;
}

interface ClientConnection {
  ws: WebSocket.WebSocket;
  subscriptions: Set<string>;
}

export class RaffleWebSocketServer {
  private wss: WebSocketServer;
  private clients: Map<WebSocket.WebSocket, ClientConnection> = new Map();

  constructor(server: Server) {
    this.wss = new WebSocketServer({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    console.log('WebSocket server initialized');
  }

  private handleConnection(ws: WebSocket.WebSocket) {
    console.log('New WebSocket connection');
    
    const client: ClientConnection = {
      ws,
      subscriptions: new Set()
    };
    
    this.clients.set(ws, client);

    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        this.handleMessage(ws, message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    ws.on('close', () => {
      console.log('WebSocket connection closed');
      this.clients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.clients.delete(ws);
    });

    // Send welcome message
    this.sendToClient(ws, {
      type: 'connection',
      data: { message: 'Connected to Raffle WebSocket server' }
    });
  }

  private handleMessage(ws: WebSocket.WebSocket, message: any) {
    const client = this.clients.get(ws);
    if (!client) return;

    switch (message.type) {
      case 'subscribe':
        if (message.eventType) {
          client.subscriptions.add(message.eventType);
          console.log(`Client subscribed to: ${message.eventType}`);
        }
        break;

      case 'unsubscribe':
        if (message.eventType) {
          client.subscriptions.delete(message.eventType);
          console.log(`Client unsubscribed from: ${message.eventType}`);
        }
        break;

      case 'subscribe_raffle':
        if (message.raffleId) {
          client.subscriptions.add(`raffle:${message.raffleId}`);
          console.log(`Client subscribed to raffle: ${message.raffleId}`);
        }
        break;

      case 'ping':
        this.sendToClient(ws, { type: 'pong', data: {} });
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  }

  private sendToClient(ws: WebSocket.WebSocket, message: any) {
    if (ws.readyState === WebSocket.WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
    }
  }

  // Broadcast to all connected clients
  broadcast(message: WebSocketMessage) {
    this.clients.forEach((client) => {
      this.sendToClient(client.ws, message);
    });
  }

  // Send to clients subscribed to a specific event type
  broadcastToSubscribers(eventType: string, message: WebSocketMessage) {
    this.clients.forEach((client) => {
      if (client.subscriptions.has(eventType)) {
        this.sendToClient(client.ws, message);
      }
    });
  }

  // Send to clients subscribed to a specific raffle
  broadcastToRaffle(raffleId: string, message: WebSocketMessage) {
    const eventType = `raffle:${raffleId}`;
    this.broadcastToSubscribers(eventType, message);
  }

  // Notify about ticket purchase
  notifyTicketPurchased(raffleId: string, ticketData: any) {
    const message: WebSocketMessage = {
      type: 'ticket_purchased',
      data: ticketData,
      raffleId
    };

    this.broadcastToRaffle(raffleId, message);
    this.broadcastToSubscribers('ticket_purchased', message);
  }

  // Notify about gift addition
  notifyGiftAdded(raffleId: string, giftData: any) {
    const message: WebSocketMessage = {
      type: 'gift_added',
      data: giftData,
      raffleId
    };

    this.broadcastToRaffle(raffleId, message);
    this.broadcastToSubscribers('gift_added', message);
  }

  // Notify about raffle update
  notifyRaffleUpdate(raffleId: string, updateData: any) {
    const message: WebSocketMessage = {
      type: 'raffle_update',
      data: updateData,
      raffleId
    };

    this.broadcastToRaffle(raffleId, message);
    this.broadcastToSubscribers('raffle_update', message);
  }

  // Notify about raffle ending
  notifyRaffleEnded(raffleId: string, endData: any) {
    const message: WebSocketMessage = {
      type: 'raffle_ended',
      data: endData,
      raffleId
    };

    this.broadcastToRaffle(raffleId, message);
    this.broadcastToSubscribers('raffle_ended', message);
  }

  // Notify about winner selection
  notifyWinnerSelected(raffleId: string, winnerData: any) {
    const message: WebSocketMessage = {
      type: 'winner_selected',
      data: winnerData,
      raffleId
    };

    this.broadcastToRaffle(raffleId, message);
    this.broadcastToSubscribers('winner_selected', message);
  }

  // Get connection statistics
  getStats() {
    return {
      totalConnections: this.clients.size,
      connections: Array.from(this.clients.values()).map(client => ({
        subscriptions: Array.from(client.subscriptions)
      }))
    };
  }

  // Close all connections
  close() {
    this.clients.forEach((client) => {
      client.ws.close();
    });
    this.wss.close();
  }
}
