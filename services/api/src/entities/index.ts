import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('raffle_service')
export class RaffleServiceEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('integer')
  serviceFeePercent: number;

  @Column('integer')
  implementerFeePercent: number;

  @Column('bigint')
  creationFee: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('raffle')
export class RaffleEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('text')
  raffleId: string;

  @Column('text')
  serviceErgoTree: string;

  @Column('text')
  implementorErgoTree: string;

  @Column('text')
  creatorErgoTree: string;

  @Column('integer')
  serviceFeePercent: number;

  @Column('integer')
  implementerFeePercent: number;

  @Column('integer')
  winnersPercent: number;

  @Column('bigint')
  ticketPrice: string;

  @Column('bigint')
  goal: string;

  @Column('integer')
  deadline: number;

  @Column('text')
  winnersPercentList: string;

  @Column('bigint')
  txFee: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('raffle_details')
export class RaffleDetailsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('text')
  raffleId: string;

  @Column('text')
  name: string;

  @Column('text')
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('picture')
export class PictureEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('integer')
  orderIndex: number;

  @Column('text')
  raffleId: string;

  @Column('text')
  content: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('ticket')
export class TicketEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('text')
  raffleId: string;

  @Column('text')
  donatorErgoTree: string;

  @Column('bigint')
  rangeStart: string;

  @Column('bigint')
  rangeEnd: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('gift')
export class GiftEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('text')
  raffleId: string;

  @Column('text')
  donatorErgoTree: string;

  @Column('integer')
  winnerIndex: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

@Entity('winner')
export class WinnerEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column('text')
  boxId: string;

  @Column('text')
  txId: string;

  @Column('text')
  serialized: string;

  @Column('text')
  raffleId: string;

  @Column('integer')
  index: number;

  @Column('integer')
  rewardPercent: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
