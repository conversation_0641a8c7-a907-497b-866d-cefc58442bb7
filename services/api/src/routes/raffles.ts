import { Router, Request, Response } from 'express';
import { RaffleService } from '../services/raffleService.js';
import { ApiResponse } from '../types/api.js';

const router = Router();
const raffleService = new RaffleService();

// GET /api/raffles - Get all raffles with pagination
router.get('/', async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    
    const result = await raffleService.getAllRaffles(page, limit);
    
    const response: ApiResponse<typeof result> = {
      success: true,
      data: result,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching raffles:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch raffles',
    };
    res.status(500).json(response);
  }
});

// GET /api/raffles/:id - Get raffle by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const raffleId = req.params.id;
    const raffle = await raffleService.getRaffleById(raffleId);
    
    if (!raffle) {
      const response: ApiResponse<null> = {
        success: false,
        error: 'Raffle not found',
      };
      return res.status(404).json(response);
    }
    
    const response: ApiResponse<typeof raffle> = {
      success: true,
      data: raffle,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching raffle:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch raffle',
    };
    res.status(500).json(response);
  }
});

// GET /api/raffles/:id/tickets - Get raffle tickets
router.get('/:id/tickets', async (req: Request, res: Response) => {
  try {
    const raffleId = req.params.id;
    const tickets = await raffleService.getRaffleTickets(raffleId);
    
    const response: ApiResponse<typeof tickets> = {
      success: true,
      data: tickets,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching tickets:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch tickets',
    };
    res.status(500).json(response);
  }
});

// GET /api/raffles/:id/gifts - Get raffle gifts
router.get('/:id/gifts', async (req: Request, res: Response) => {
  try {
    const raffleId = req.params.id;
    const gifts = await raffleService.getRaffleGifts(raffleId);
    
    const response: ApiResponse<typeof gifts> = {
      success: true,
      data: gifts,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching gifts:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch gifts',
    };
    res.status(500).json(response);
  }
});

// GET /api/raffles/:id/winners - Get raffle winners
router.get('/:id/winners', async (req: Request, res: Response) => {
  try {
    const raffleId = req.params.id;
    const winners = await raffleService.getRaffleWinners(raffleId);
    
    const response: ApiResponse<typeof winners> = {
      success: true,
      data: winners,
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching winners:', error);
    const response: ApiResponse<null> = {
      success: false,
      error: 'Failed to fetch winners',
    };
    res.status(500).json(response);
  }
});

export default router;
