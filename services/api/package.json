{"name": "@ergo-raffle/api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"@ergo-raffle/extractors": "^0.1.0", "@rosen-bridge/extended-typeorm": "^0.0.3", "@rosen-bridge/winston-logger": "^1.0.3", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "config": "^3.3.12", "typeorm": "0.3.20", "sqlite3": "^5.1.7", "reflect-metadata": "^0.1.14", "ws": "^8.14.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/config": "^3.3.3", "@types/ws": "^8.5.8", "tsx": "^4.19.3", "typescript": "^5.3.3", "vitest": "^1.2.0"}}