{"name": "raffle-v2", "private": true, "type": "module", "workspaces": {"packages": ["services/*", "packages/*", "frontend"]}, "scripts": {"build": "npm run build --workspaces", "coverage": "npm run coverage --workspaces", "lint": "npm run lint --workspaces", "prepare": "husky install", "release": "npm run release --workspaces", "test": "npm run test --workspaces", "test:related": "npm run test:related --workspaces", "type-check": "npm run type-check --workspaces", "version": "npx changeset version && npm i", "dev": "./start-dev.sh", "dev:api": "cd services/api && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build:api": "cd services/api && npm run build", "build:frontend": "cd frontend && npm run build"}, "devDependencies": {"@changesets/cli": "^2.27.1", "@rosen-bridge/changeset-formatter": "^0.1.0", "@types/node": "^20.11.0", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "husky": "^8.0.0", "lint-staged": "^13.0.3", "prettier": "^3.2.5", "tsx": "^4.19.3"}}