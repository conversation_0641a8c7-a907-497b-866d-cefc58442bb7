#!/bin/bash

# Ergo Raffle V2 Development Startup Script

echo "🎲 Starting Ergo Raffle V2 Development Environment"
echo "=================================================="

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port $1 is already in use"
        return 1
    else
        return 0
    fi
}

# Check if required ports are available
echo "🔍 Checking ports..."
if ! check_port 3001; then
    echo "❌ API port 3001 is in use. Please stop the existing service."
    exit 1
fi

if ! check_port 3000; then
    echo "❌ Frontend port 3000 is in use. Please stop the existing service."
    exit 1
fi

echo "✅ Ports are available"

# Start API server in background
echo "🚀 Starting API server..."
cd services/api
npm run dev &
API_PID=$!
cd ../..

# Wait a moment for API to start
sleep 3

# Start frontend server in background
echo "🎨 Starting frontend..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 Development environment started!"
echo "=================================="
echo "📡 API Server: http://localhost:3001"
echo "🌐 Frontend:   http://localhost:3000"
echo ""
echo "📋 Available API endpoints:"
echo "   GET /health - Health check"
echo "   GET /api/raffles - List all raffles"
echo "   GET /api/raffles/:id - Get raffle details"
echo "   GET /api/raffles/:id/tickets - Get raffle tickets"
echo "   GET /api/raffles/:id/gifts - Get raffle gifts"
echo "   GET /api/raffles/:id/winners - Get raffle winners"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $API_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Services stopped"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for background processes
wait
