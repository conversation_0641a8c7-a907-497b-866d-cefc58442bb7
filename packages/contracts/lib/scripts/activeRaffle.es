{
  // ErgoRaffle V2 Active Raffle Contract
  //
  // Registers:
  //   R4[Coll[Long]]: [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ee<PERSON>ercent, I<PERSON>ment<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ticket<PERSON>rice, Goal, Deadline, txFee]
  //   R5[Coll[Coll[Byte]]]: [ServiceErgoTreeHash, ImplementerErgoTreeHash, ProjectErgoTreeHash]
  //   R6[Int]: WinnersCount
  //   R7[Long]: TotalSoldTickets
  // Tokens:
  //   0: RaffleLicense
  //   1: Ticket
  //   2: CollectingToken (if token-goal raffle)
  // Context (donation):
  //   C0: Coll[Coll[Byte]]: [DonatorErgoTree]
  // Context (successful end):
  //   C0: Coll[Coll[Byte]]: [ServiceErgoTree, ImplementerErgoTree]
  //
  // Spent in 3 transactions:
  //   - Donation
  //      [ActiveRaffle, UserBox] --> [ActiveRaffle, Ticket]
  //   - Successful end
  //      [ActiveRaffle, RaffleDetail] + [(DataInput)Oracle] --> [SuccessRaffle, ServiceFee, I<PERSON>menter<PERSON>ee]
  //   - Failure end
  //      [ActiveRaffle, RaffleDetail] --> [GiftRedeem]
  // 
  val oracleTokenId = fromBase64("ORACLE_TOKEN_ID_B64")
  val successRaffleScriptHash = fromBase64("SUCCESS_RAFFLE_SCRIPT_HASH_B64")
  val giftRedeemScriptHash = fromBase64("GIFT_REDEEM_SCRIPT_HASH_B64")
  val ticketScriptHash = fromBase64("TICKET_SCRIPT_HASH_B64")
  val safePayScriptHash = fromBase64("SAFE_PAY_SCRIPT_HASH_B64")

  val isErgGoal = (SELF.tokens.size == 2)
  val ticketPrice = SELF.R4[Coll[Long]].get(3)
  val goal = SELF.R4[Coll[Long]].get(4)
  val deadline = SELF.R4[Coll[Long]].get(5)
  val txFee = SELF.R4[Coll[Long]].get(6)
  val winnersCount = SELF.R6[Int].get
  val totalSoldTickets = SELF.R7[Long].get
  val totalRaised = ticketPrice * totalSoldTickets

  if(HEIGHT < deadline){
    // Donation
    // [ActiveRaffle, UserBox] --> [ActiveRaffle, Ticket]
    val outputRaffle = OUTPUTS(0)
    val ticket = OUTPUTS(1)
    val onSaleTickets = ticket.tokens(0)._2
    val depositTicketPrice = if(isErgGoal) {
      outputRaffle.value >= SELF.value + ticketPrice * onSaleTickets
    } else {
      outputRaffle.tokens(2)._1 == SELF.tokens(2)._1 &&
      outputRaffle.tokens(2)._2 >= SELF.tokens(2)._2 + ticketPrice * onSaleTickets &&
      outputRaffle.value >= SELF.value
    }
    sigmaProp(allOf(Coll(
      // Correct ActiveRaffle format
      outputRaffle.propositionBytes == SELF.propositionBytes,
      outputRaffle.tokens(0) == SELF.tokens(0),
      outputRaffle.tokens(1)._1 == SELF.tokens(1)._1,
      outputRaffle.tokens(1)._2 == SELF.tokens(1)._2 - onSaleTickets,
      outputRaffle.R4[Coll[Long]].get == SELF.R4[Coll[Long]].get,
      outputRaffle.R5[Coll[Coll[Byte]]].get == SELF.R5[Coll[Coll[Byte]]].get,
      outputRaffle.R6[Int].get == SELF.R6[Int].get,
      outputRaffle.R7[Long].get == totalSoldTickets + onSaleTickets,
      outputRaffle.tokens.size == SELF.tokens.size,
      depositTicketPrice,

      // Correct Ticket format
      // R4: [DonatorErgoTreeHash]
      // R5: [RangeStart, RangeEnd, TicketPrice, deadline]
      blake2b256(ticket.propositionBytes) == ticketScriptHash,
      ticket.value >= 3 * txFee,
      ticket.tokens(0)._1 == SELF.tokens(1)._1,
      ticket.R4[Coll[Byte]].get == blake2b256(getVar[Coll[Coll[Byte]]](0).get(0)),
      ticket.R5[Coll[Long]].get == Coll[Long](
        totalSoldTickets, 
        totalSoldTickets + onSaleTickets, 
        ticketPrice,
        deadline
      ),
    )))
  } else if (totalRaised >= goal) {
    // Successful end
    // [ActiveRaffle, RaffleDetail] + [(DataInput)Oracle] --> [SuccessRaffle, ServiceFee, ImplementerFee]
    val successRaffle = OUTPUTS(0)
    val serviceFee = OUTPUTS(1)
    val implementerFee = OUTPUTS(2)
    val oracleBox = CONTEXT.dataInputs(0)
    val winnersPercent = SELF.R4[Coll[Long]].get(0)
    val serviceFeePercent = SELF.R4[Coll[Long]].get(1)
    val implementerFeePercent = SELF.R4[Coll[Long]].get(2)
    val serviceErgoTreeHash = SELF.R5[Coll[Coll[Byte]]].get(0)
    val implementorErgoTreeHash = SELF.R5[Coll[Coll[Byte]]].get(1)
    val projectErgoTreeHash = SELF.R5[Coll[Coll[Byte]]].get(2)
    val serviceErgoTree = getVar[Coll[Coll[Byte]]](0).get(0)
    val implementerErgoTree = getVar[Coll[Coll[Byte]]](0).get(1)
    val splittingRaisedFund = if(isErgGoal) { 
      serviceFee.value == (totalRaised * serviceFeePercent) / 1000 + 2 * txFee &&
      implementerFee.value == (totalRaised * implementerFeePercent) / 1000 + 2 * txFee
    } else {
      successRaffle.tokens(2)._1 == SELF.tokens(2)._1 &&
      serviceFee.tokens(0)._1 == SELF.tokens(2)._1 &&
      implementerFee.tokens(0)._1 == SELF.tokens(2)._1 &&
      serviceFee.tokens(0)._2 == (totalRaised * serviceFeePercent) / 1000 &&
      implementerFee.tokens(0)._2 == (totalRaised * implementerFeePercent) / 1000 &&
      successRaffle.tokens(2)._2 >= 
        SELF.tokens(2)._2 - serviceFee.tokens(0)._2 - implementerFee.tokens(0)._2 &&
      serviceFee.value == 2 * txFee &&
      implementerFee.value == 2 * txFee
    }
    sigmaProp(allOf(Coll(
      // Correct Oracle box
      oracleBox.tokens(0)._1 == oracleTokenId,
      oracleBox.creationInfo._1 > deadline,

      // Correct SuccessRaffle format
      // R4: [TotalPrize, TotalSoldTickets]
      // R5: WinnersCount
      // R6: [Seed, SelectedWinnersListHash]
      // R7: Step
      blake2b256(successRaffle.propositionBytes) == successRaffleScriptHash,
      successRaffle.value >= SELF.value - serviceFee.value - implementerFee.value,
      successRaffle.tokens(0) == SELF.tokens(0),
      successRaffle.tokens(1)._1 == SELF.tokens(1)._1,
      successRaffle.tokens(1)._2 == SELF.tokens(1)._2 + 1,
      successRaffle.tokens.size == SELF.tokens.size,
      successRaffle.R4[Coll[Long]].get == Coll[Long](
        totalRaised * winnersPercent / 1000,
        totalSoldTickets,
        txFee
      ),
      successRaffle.R5[Int].get == winnersCount,
      successRaffle.R6[Coll[Byte]].get == projectErgoTreeHash,
      successRaffle.R7[Coll[Coll[Byte]]].get(0) == oracleBox.id,
      successRaffle.R7[Coll[Coll[Byte]]].get(1) == blake2b256(Coll[Byte]()),
      successRaffle.R8[Int].get == 1,

      // Correct Fee Safe Payments
      blake2b256(serviceFee.propositionBytes) == safePayScriptHash,
      serviceFee.R4[Coll[Byte]].get == serviceErgoTreeHash,
      serviceFee.R5[Long].get == txFee,
      blake2b256(serviceErgoTree) == serviceFee.R4[Coll[Byte]].get,
      blake2b256(implementerFee.propositionBytes) == safePayScriptHash,
      implementerFee.R4[Coll[Byte]].get == implementorErgoTreeHash,
      implementerFee.R5[Long].get == txFee,
      blake2b256(implementerErgoTree) == implementerFee.R4[Coll[Byte]].get,

      // Transaction constraints
      splittingRaisedFund,
    )))
  } else {
    // Failure end
    // [ActiveRaffle, RaffleDetail] --> [GiftRedeem]
    val giftRedeem = OUTPUTS(0)
    val collectingTokenCheck = 
      if(!isErgGoal) giftRedeem.tokens(2) == SELF.tokens(2) else true
    sigmaProp(allOf(Coll(
      // Correct GiftRedeem format
      // R4: [TotalSoldTicket, TicketPrice, txFee]
      // R5: WinnersCount
      // R6: Step
      blake2b256(giftRedeem.propositionBytes) == giftRedeemScriptHash,
      giftRedeem.value == SELF.value,
      giftRedeem.tokens(0) == SELF.tokens(0),
      giftRedeem.tokens(1)._1 == SELF.tokens(1)._1,
      giftRedeem.tokens(1)._2 == SELF.tokens(1)._2 + 1,
      collectingTokenCheck,
      giftRedeem.tokens.size == SELF.tokens.size,
      giftRedeem.R4[Coll[Long]].get == Coll[Long](
        totalSoldTickets, 
        ticketPrice, 
        txFee
      ),
      giftRedeem.R5[Int].get == winnersCount,
      giftRedeem.R6[Int].get == 1,
    )))
  }
}
