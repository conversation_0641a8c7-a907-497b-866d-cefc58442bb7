{"name": "@ergo-raffle/contracts", "version": "0.1.0", "description": "Raffle v2 related contracts", "license": "GPL-3.0", "author": "Ergo Raffle Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "mkdir -p dist/ && tsx --trace-warnings ./cli/index.ts build configs.json", "build-testnet": "npm run build -- --testnet", "coverage": "npm run test -- --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore --ignore-path ./.prettierignore", "release": "npm run test -- --run && npm run build && npm publish --access public", "test": "NODE_OPTIONS='--import tsx' vitest", "test:related": "NODE_OPTIONS='--import tsx' vitest related --run", "type-check": "tsc --noEmit", "compile-contracts": "tsx ./cli/index.ts compile-contracts", "make-input-template": "tsx ./cli/index.ts make-input-template", "make-build-template": "tsx ./cli/index.ts make-input-template --for-build"}, "devDependencies": {"@fleet-sdk/mock-chain": "^0.4.2", "@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "dependencies": {"@fleet-sdk/common": "^0.4.1", "@fleet-sdk/compiler": "^0.4.2", "@fleet-sdk/core": "^0.4.1", "@fleet-sdk/crypto": "^0.4.1", "@fleet-sdk/serializer": "^0.4.1", "commander": "^12.1.0", "sigmastate-js": "^0.4.6", "winston": "^3.13.0"}, "engines": {"node": ">=20.11.0"}}