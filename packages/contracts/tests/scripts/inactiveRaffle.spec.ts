import { it, describe, expect } from 'vitest';
import { mockUTxO } from '@fleet-sdk/mock-chain';
import { SColl, SInt, SLong, SByte } from '@fleet-sdk/serializer';
import { TransactionBuilder, OutputBuilder } from '@fleet-sdk/core';

import * as constants from '../../constants';
import * as testUtils from '../testUtils';
import {
  X_TOKEN_ID,
  CREATOR_DEFAULT_BALANCE,
  UNKNOWN_WALLET_DEFAULT_BALANCE,
} from '../testUtils';
import { ScriptNamesType } from '../../lib/types';

/*
 * create fixtures that contains below steps data:
 *   - mock chain and partners
 *   - compile contracts
 *   - create inactiveRaffle input box
 *   - create ticketRepo input box
 * @returns vitest customized "it" object
 */
function createInactiveRaffleTest(winnersCount: number = 1) {
  const boxFactory = new testUtils.RaffleBoxFactory(
    { height: 1000 },
    constants.scriptList.filter(
      (value) => value != 'inactiveRaffle',
    ) as ScriptNamesType[],
  );
  const { creator, someone } = boxFactory.createPartners({
    creator: CREATOR_DEFAULT_BALANCE,
    someone: UNKNOWN_WALLET_DEFAULT_BALANCE,
  });

  const ticketRepoInputBox = boxFactory.createTicketRepoBoxMock();
  const inactiveRaffleInputBox = boxFactory.createInactiveRaffleBoxMock(
    creator.ergoTree,
    someone.ergoTree,
    creator.ergoTree,
    winnersCount,
  );

  return it.extend({
    boxFactory: boxFactory,
    someoneWallet: someone,
    creator: creator,
    ticketRepoInputBox: ticketRepoInputBox,
    inactiveRaffleInputBox: inactiveRaffleInputBox,
  });
}

describe('inactiveRaffle', () => {
  const inactiveRaffleBy1WinnerTest = createInactiveRaffleTest();
  const inactiveRaffleBy5WinnersTest = createInactiveRaffleTest(5);

  describe('Active raffle creation', () => {
    /**
     * @target inactive-raffle should create active raffle by 1 winner successfully
     * @scenario
     * - create three output boxes by valid values and one winner box
     * - execute transaction
     * - check execution done successfully
     * @expected
     * - transaction result must be true
     */
    inactiveRaffleBy1WinnerTest(
      'should create active raffle by 1 winner successfully',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        const winnersBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        const res = boxFactory.chain.execute(transaction, {
          signers: [creator],
        });
        // Check execution result
        expect(res).true;
      },
    );

    /**
     * @target inactive-raffle should create active raffle by 5 winner
     * @scenario
     * - create three output boxes by valid values and 5 winners boxes
     * - execute transaction
     * - check execution done successfully
     * @expected
     * - transaction result must be true
     */
    inactiveRaffleBy5WinnersTest(
      'should create active raffle by 5 winner',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          5,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(5);
        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              5,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        const res = boxFactory.chain.execute(transaction, {
          signers: [creator],
        });
        // Check execution result
        expect(res).true;
      },
    );

    /**
     * @target inactive-raffle should create active raffle by 1 winner and X token-goal
     * @scenario
     * - create three output boxes by valid values and one winner box
     * - execute transaction
     * - check execution done successfully
     * @expected
     * - transaction result must be true
     */
    inactiveRaffleBy1WinnerTest(
      'should create active raffle by 1 winner and X token-goal',
      ({ boxFactory, someoneWallet, creator, ticketRepoInputBox }) => {
        const inactiveRaffleInputBox = boxFactory.createInactiveRaffleBoxMock(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          { tokenId: X_TOKEN_ID, amount: 1n },
        );
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          undefined,
          { tokenId: X_TOKEN_ID, amount: 1n },
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const winnersBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersBoxes,
          ])
          .payFee(testUtils.FEE)
          .build();

        const res = boxFactory.chain.execute(transaction, {
          signers: [creator],
        });
        // Check execution result
        expect(res).true;
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by missed license-token
     * @scenario
     * - create three output boxes by valid values and one winner box(remove license-token from active box and added to gift box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create token by missed license-token',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );

        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // remove license token
        activeRaffleOutputBox.assets.remove(testUtils.LICENSE_TOKEN_ID);
        giftTokenRepoOutputBox.assets.add({
          tokenId: testUtils.LICENSE_TOKEN_ID,
          amount: 1n,
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong ticket token
     * @scenario
     * - create three output boxes by valid values and one winner box(move one ticket token from active box to gift box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong ticket token',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );

        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const winnerBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        // Move one ticket token from Active-Raffle box to the Gift-Token-Repo Box
        activeRaffleOutputBox.assets.at(1).amount =
          activeRaffleOutputBox.assets.at(1).amount - 1n;
        giftTokenRepoOutputBox.addTokens({
          tokenId: activeRaffleOutputBox.assets.at(1).tokenId as string,
          amount: 1n,
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnerBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong R4 of active raffle
     * @scenario
     * - create three output boxes by valid values and one winner box(set invalid value on the R4 of active box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create by wrong R4 of active raffle',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Set invalid value as R4 data
        activeRaffleOutputBox.setAdditionalRegisters({
          R4: SColl(SLong, [5n, 6n]).toHex(),
          R5: SColl(SColl(SByte), [
            Array.from(
              Buffer.from(
                (boxFactory.contractsAddresses as { [k: string]: string })[
                  'service'
                ],
                'hex',
              ),
            ),
            Array.from(Buffer.from(someoneWallet.ergoTree)),
            Array.from(Buffer.from(creator.ergoTree)),
          ]),
          R6: SColl(SLong, [0n]).toHex(),
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong R5 of active raffle
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong R5 value on the active raffle box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create by wrong R5 of active raffle',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // replace invalid R5 value of the Active-Raffle box
        activeRaffleOutputBox.setAdditionalRegisters({
          R4: SColl(SLong, [
            200n, // WinnersPercent,
            100n, // ServiceFeePercent,
            100n, // ImplementerFeePercent,
            10n, // TicketPrice,
            1000n, // Goal,
            0n, // Deadline,
            1n, // WinnersCount,
            testUtils.FEE, // TxFee
          ]).toHex(),
          R5: SColl(SLong, [5n, 6n]).toHex(),
          R6: SColl(SLong, [0n]).toHex(),
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong value of active raffle
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong value of active raffle box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create by wrong value of active raffle',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Set invalid value for the active-raffle box
        activeRaffleOutputBox.setValue(150_000n);

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong collecting token on the active-box
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong collecting token on the active-box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong collecting token on the inactive-box',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        // Set collecting token as X-Token that not found on the Inactive-Raffle Box
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          undefined,
          { tokenId: X_TOKEN_ID, amount: 1n }, // set collecting token
        );
        const extraInputBox = mockUTxO({
          ergoTree: creator.ergoTree,
          value: 11_000_000n,
          creationHeight: 4,
          assets: [{ tokenId: X_TOKEN_ID, amount: 1n }],
        });
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox, extraInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong collecting token on the inactive-box
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong collecting token on the inactive-box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong collecting token on the active-box',
      ({ boxFactory, someoneWallet, creator, ticketRepoInputBox }) => {
        const inactiveRaffleInputBox = boxFactory.createInactiveRaffleBoxMock(
          boxFactory.contractsAddresses['service'],
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          { tokenId: X_TOKEN_ID, amount: 1n }, // Set collecting token as X-Token that missed on the active box
        );

        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        // added X-Token to the gift box to prevent burn token error raising
        giftTokenRepoOutputBox.addTokens({ tokenId: X_TOKEN_ID, amount: 1n });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong winner box percentage
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong winner box percentage)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong winner box percentage',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const winnersBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        // Set winner box wrong percentage on the R4 second cell
        winnersBoxes[0].setAdditionalRegisters({
          R4: SColl(SLong, [BigInt(1), 2000n, 0n, testUtils.FEE]),
        });
        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong winner box index
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong winner box index)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong winner box index',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const winnersBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        // Set winner box wrong index on the R4 first cell
        winnersBoxes[0].setAdditionalRegisters({
          R4: SColl(SLong, [BigInt(43), 1000n, 0n, testUtils.FEE]),
        });
        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong winner box ticket-token
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong winner box ticket-token)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong winner box ticket-token',
      ({ boxFactory, someoneWallet, creator, ticketRepoInputBox }) => {
        const inactiveRaffleInputBox = boxFactory.createInactiveRaffleBoxMock(
          boxFactory.contractsAddresses['service'],
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          { tokenId: X_TOKEN_ID, amount: 1n },
        );
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const winnersBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );

        // Replace wrong ticket-token data
        winnersBoxes[0].assets.remove(testUtils.TICKET_TOKEN_ID);
        winnersBoxes[0].assets.add({
          tokenId: testUtils.X_TOKEN_ID,
          amount: 1n,
        });
        activeRaffleOutputBox.assets.remove(testUtils.TICKET_TOKEN_ID);
        activeRaffleOutputBox.addTokens({
          tokenId: testUtils.TICKET_TOKEN_ID,
          amount: 1_000_000_000n - 1n - 1n + 1n, // at last + 1n added to prevent burn token error
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle
     * by wrong raffle-details box without ticket token
     * @scenario
     * - create three output boxes by valid values and one winner box(remove ticket token from raffle-details box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong raffle-details box without ticket token',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();

        // Remove ticket token from raffle-details box
        raffleDetailsOutputBox.assets.remove(0);
        activeRaffleOutputBox.assets.remove(testUtils.TICKET_TOKEN_ID);
        activeRaffleOutputBox.addTokens({
          tokenId: testUtils.TICKET_TOKEN_ID,
          amount: 1_000_000_000n - 1n - 1n + 1n, // at last + 1n added to prevent burn token error
        });

        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);
        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong raffle-details box R4 value
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong raffle-details box R4 value)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong raffle-details box R4 value',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Replace invalid R4 value from raffle-details box
        raffleDetailsOutputBox.setAdditionalRegisters({
          R4: SColl(SColl(SByte), [
            Array.from(Buffer.from('Invalid Name')),
            Array.from(Buffer.from('Some invalid descriptions...')),
          ]).toHex(),
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to when creating the giftTokenRepo without gift tokens
     * @scenario
     * - create three valid output boxes for activeRaffle, raffleDetail and winner box
     * - create giftTokenRepo without gift tokens
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail when creating the giftTokenRepo without gift tokens',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox = boxFactory.createGiftTokenRepoOutputBox(
          1,
          // preventing of minting token of gift-token box
          null,
        );

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail to create active raffle by wrong R7 value of gift-token box
     * @scenario
     * - create three output boxes by valid values and one winner box(set wrong R7 value of gift-token box)
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail create active raffle by wrong R7 value of gift-token box',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Place wrong R7 value to the gift-token box
        giftTokenRepoOutputBox.setAdditionalRegisters({
          R4: SColl(SInt, [1]).toHex(),
          R5: SColl(SInt, [2]).toHex(),
          R6: SColl(SInt, [3]).toHex(),
          R7: SColl(SInt, [1, 1, Number(testUtils.FEE)]).toHex(),
          R8: SColl(SByte, Array.from(Buffer.from('abcd', 'hex'))),
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target inactive-raffle should fail trying to steal gift tokens in a new utxo
     * @scenario
     * - create all valid outputs (activeRaffle, raffleDetail, giftTokenRepo and winner box)
     * - mock an extra input UTxO to cover the extra output
     * - add an extra output stealing one gift token
     * - execute transaction
     * - check execution result
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail trying to steal gift tokens in a new utxo',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Create input and output box required for this test
        const extraInput = mockUTxO({
          ergoTree: creator.ergoTree,
          value: 150_000n,
          creationHeight: 10,
        });
        const changeBox = new OutputBuilder(
          150_000n,
          creator.ergoTree,
        ).addTokens({
          tokenId: inactiveRaffleInputBox.boxId,
          amount: 1n,
        });

        const transaction = new TransactionBuilder(boxFactory.chain.height)
          .from([
            inactiveRaffleInputBox,
            ticketRepoInputBox,
            // Add extra nano-erg as input
            extraInput,
          ])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...boxFactory.createWinnersOutputBox(
              1,
              inactiveRaffleInputBox.boxId.toString(),
            ),
            changeBox,
          ])
          .payFee(testUtils.FEE)
          .build();

        // Check execution result
        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target should fail creating of active raffle by 1 winner with invalid ticket token id
     * @scenario
     * - create three output boxes(by invalid ticket-token in active box)
     * - execute transaction
     * - check execution must raise error
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail creating of active raffle by 1 winner with invalid ticket token id',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const winnersOutputBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Replace Ticket-Token with another token
        const extraInputBox = mockUTxO({
          value: testUtils.FEE,
          ergoTree: creator.ergoTree,
          assets: [
            {
              tokenId: '12'.repeat(32),
              amount: 1_000_000_000n,
            },
          ],
        });
        activeRaffleOutputBox.assets.remove(1);
        activeRaffleOutputBox.assets.add({
          tokenId: '12'.repeat(32),
          amount: 1_000_000_000n - 1n - 1n,
        });

        const transaction = new TransactionBuilder(1000)
          .from([inactiveRaffleInputBox, ticketRepoInputBox, extraInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersOutputBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target should fail creating of active raffle by 1 winner with invalid number of ticket token
     * @scenario
     * - create three output boxes(by invalid number of ticket token in active box)
     * - execute transaction
     * - check execution must raise error
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail creating of active raffle by 1 winner with invalid number of ticket token',
      ({
        boxFactory,
        someoneWallet,
        creator,
        ticketRepoInputBox,
        inactiveRaffleInputBox,
      }) => {
        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          undefined,
          undefined,
          // Decreasing Ticket-Token number sets in activeRaffleOutputBox
          1_000_000_000n,
          undefined,
          999_999_997n,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const winnersOutputBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        // Move one extra Ticket-Token to the giftTokenRepoOutputBox
        giftTokenRepoOutputBox.assets.add({
          tokenId: testUtils.TICKET_TOKEN_ID,
          amount: 1n,
        });

        const transaction = new TransactionBuilder(1000)
          .from([inactiveRaffleInputBox, ticketRepoInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersOutputBoxes,
          ])
          .payFee(testUtils.FEE)
          // .sendChangeTo(creator.address)
          .build();

        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );

    /**
     * @target should fail creating of active raffle by 1 winner with invalid ticket token id in R7 of inactive input box
     * @scenario
     * - create three output boxes(by invalid ticket-token id in inactive input box)
     * - execute transaction
     * - check execution must raise error
     * @expected
     * - transaction result must throw error
     */
    inactiveRaffleBy1WinnerTest(
      'should fail creating of active raffle by 1 winner with invalid ticket token id in R7 of inactive input box',
      ({ boxFactory, someoneWallet, creator, ticketRepoInputBox }) => {
        // Replace Ticket-Token id with invalid id
        const inactiveRaffleInputBox = boxFactory.createInactiveRaffleBoxMock(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
          1,
          undefined,
          undefined,
          undefined,
          undefined,
          1_000_000_000n,
          '1234'.repeat(16),
          0n,
        );
        const extraInputBox = mockUTxO({
          value: testUtils.FEE,
          ergoTree: creator.ergoTree,
          assets: [
            {
              tokenId: '1234'.repeat(16),
              amount: 1_000_000_000n,
            },
          ],
        });

        const activeRaffleOutputBox = boxFactory.createActiveRaffleOutputBox(
          creator.ergoTree,
          someoneWallet.ergoTree,
          creator.ergoTree,
        );
        const raffleDetailsOutputBox =
          boxFactory.createRaffleDetailsOutputBox();
        const winnersOutputBoxes = boxFactory.createWinnersOutputBox(
          1,
          inactiveRaffleInputBox.boxId.toString(),
        );
        const giftTokenRepoOutputBox =
          boxFactory.createGiftTokenRepoOutputBox(1);

        const transaction = new TransactionBuilder(1000)
          .from([inactiveRaffleInputBox, ticketRepoInputBox, extraInputBox])
          .to([
            activeRaffleOutputBox,
            raffleDetailsOutputBox,
            giftTokenRepoOutputBox,
            ...winnersOutputBoxes,
          ])
          .payFee(testUtils.FEE)
          .sendChangeTo(creator.address)
          .build();

        expect(() =>
          boxFactory.chain.execute(transaction, { signers: [creator] }),
        ).toThrowError();
      },
    );
  });
});
