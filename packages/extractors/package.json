{"name": "@ergo-raffle/extractors", "version": "0.1.0", "description": "Raffle v2 related extractors", "repository": "", "license": "GPL-3.0", "author": "Ergo Raffle Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig.build.json", "coverage": "npm run test -- --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "release": "npm run build && npm publish --access public", "test": "NODE_OPTIONS='--import tsx' vitest", "type-check": "tsc --noEmit", "typeorm": "NODE_OPTIONS='--import tsx' typeorm", "typeorm:generate:postgres": "npm run typeorm migration:generate ./lib/migrations/postgres/postgres -- -p ", "typeorm:generate:sqlite": "npm run typeorm migration:generate ./lib/migrations/sqlite/sqlite -- -p "}, "dependencies": {"@fleet-sdk/core": "^0.4.1", "@fleet-sdk/serializer": "^0.4.1", "@rosen-bridge/abstract-extractor": "^2.0.0-8792333e", "@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/extended-typeorm": "^0.0.3", "@rosen-bridge/json-bigint": "^0.1.0", "@rosen-bridge/scanner": "^6.0.0-8792333e", "@rosen-bridge/scanner-interfaces": "^0.1.0-8792333e", "@rosen-bridge/winston-logger": "^1.0.2", "lodash-es": "^4.17.21", "pg": "^8.4.0", "reflect-metadata": "^0.1.14", "sqlite3": "^5.1.7", "typeorm": "0.3.20"}, "devDependencies": {"@fleet-sdk/compiler": "^0.4.2", "@fleet-sdk/mock-chain": "^0.4.2", "@types/json-bigint": "^1.0.4", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=20.11.0"}}