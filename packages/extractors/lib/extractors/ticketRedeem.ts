import { DataSource } from 'typeorm';
import { AbstractLogger } from '@rosen-bridge/abstract-logger';
import { AbstractInitializableErgoExtractor } from '@rosen-bridge/abstract-extractor';
import { OutputBox, ErgoNetworkType } from '@rosen-bridge/scanner-interfaces';

import { TicketRedeemAction } from '../actions/ticketRedeem';
import { TicketRedeemBoxInterface } from '../interfaces/types';
import { TicketRedeemEntity } from '../entities';
import { ErgoAddress, Box } from '@fleet-sdk/core';
import { SConstant, serializeBox } from '@fleet-sdk/serializer';

export class TicketRedeemExtractor extends AbstractInitializableErgoExtractor<
  TicketRedeemBoxInterface,
  TicketRedeemEntity
> {
  readonly actions: TicketRedeemAction;
  private readonly id: string;
  private readonly ergoTree: string;
  private readonly raffleLicenseId: string;

  constructor(
    dataSource: DataSource,
    id: string,
    url: string,
    type: ErgoNetworkType,
    address: string,
    raffleLicenseId: string,
    logger?: AbstractLogger,
    initialize = true,
  ) {
    super(type, url, address, logger, initialize);
    this.id = id;
    this.ergoTree = ErgoAddress.fromBase58(address).ergoTree.toString();
    this.raffleLicenseId = raffleLicenseId;
    this.actions = new TicketRedeemAction(dataSource, this.logger);
  }

  /**
   * get Id for current extractor
   */
  getId = () => `${this.id}`;

  /**
   * check proper data format in the box
   * @param box
   * @return true if the box has the required data and false otherwise
   */
  hasData = (box: OutputBox): boolean => {
    try {
      return (
        box.ergoTree == this.ergoTree &&
        box.additionalRegisters.R4 != undefined &&
        (SConstant.from(box.additionalRegisters.R4).data as bigint[]).length ==
          3 &&
        box.additionalRegisters.R5 != undefined &&
        (SConstant.from(box.additionalRegisters.R5).data as bigint) !=
          undefined &&
        box.assets.length > 1 &&
        box.assets[0].tokenId == this.raffleLicenseId
      );
    } catch (err) {
      this.logger.error(`TicketRedeemExtractor Error: ${err}`);
      return false;
    }
  };

  /**
   * extract box data to proper format (not including spending information)
   * @param box
   * @return extracted data in proper format
   */
  extractBoxData = (box: OutputBox): TicketRedeemBoxInterface | undefined => {
    const r4Register = SConstant.from(box.additionalRegisters.R4!)
      .data as bigint[];
    const redeemedTickets = SConstant.from(box.additionalRegisters.R5!)
      .data as bigint;

    const data = {
      boxId: box.boxId.toString(),
      txId: box.transactionId,
      raffleId: box.assets[1].tokenId,
      totalSoldTicket: r4Register[0],
      redeemedTickets: redeemedTickets,
      serialized: Buffer.from(serializeBox(box as Box).toBytes()).toString(
        'base64',
      ),
    };

    return data;
  };
}
