import { DataSource } from 'typeorm';
import { AbstractLogger } from '@rosen-bridge/abstract-logger';
import {
  AbstractInitializableErgoExtractor,
  boxHasToken,
} from '@rosen-bridge/abstract-extractor';
import {
  OutputBox,
  ErgoNetworkType,
  InputExtension,
} from '@rosen-bridge/scanner-interfaces';

import { InactiveRaffleAction } from '../actions/inactiveRaffle';
import { InactiveRaffleBoxInterface } from '../interfaces/types';
import { RaffleEntity } from '../entities';
import { Box, ErgoAddress } from '@fleet-sdk/core';
import { SConstant, serializeBox } from '@fleet-sdk/serializer';

export class InactiveRaffleExtractor extends AbstractInitializableErgoExtractor<
  InactiveRaffleBoxInterface,
  RaffleEntity
> {
  readonly actions: InactiveRaffleAction;
  private readonly id: string;
  private readonly ergoTree: string;
  private readonly licenseTokenId: string;
  private readonly serviceErgoTree: string;

  constructor(
    dataSource: DataSource,
    id: string,
    url: string,
    address: string,
    serviceAddress: string,
    licenseTokenId: string,
    logger?: AbstractLogger,
    initialize = true,
  ) {
    super(ErgoNetworkType.Node, url, address, logger, initialize);
    this.id = id;
    this.ergoTree = ErgoAddress.fromBase58(address).ergoTree.toString();
    this.actions = new InactiveRaffleAction(dataSource, this.logger);
    this.serviceErgoTree =
      ErgoAddress.fromBase58(serviceAddress).ergoTree.toString();
    this.licenseTokenId = licenseTokenId;
  }

  /**
   * get Id for current extractor
   */
  getId = () => `${this.id}`;

  /**
   * check proper data format in the box
   * @param box
   * @return true if the box has the required data and false otherwise
   */
  hasData = (box: OutputBox): boolean => {
    try {
      return (
        box.ergoTree == this.ergoTree &&
        boxHasToken(box, [this.licenseTokenId]) &&
        (SConstant.from(box.additionalRegisters!.R4!).data as bigint[])
          .length == 7 &&
        (SConstant.from(box.additionalRegisters!.R7!).data as Uint8Array[])
          .length == 2
      );
    } catch (err) {
      this.logger.error(`InactiveRaffleExtractor Error: ${err}`);
      return false;
    }
  };

  /**
   * extract box data to proper format (not including spending information)
   * @param box
   * @param inputExtensions
   * @return extracted data in proper format
   */
  extractBoxData = (
    box: OutputBox,
    inputExtensions?: InputExtension[],
  ): InactiveRaffleBoxInterface | undefined => {
    const R4Serialized = SConstant.from(box.additionalRegisters!.R4!)
      .data as bigint[];

    const R7Serialized = SConstant.from(box.additionalRegisters!.R7!)
      .data as Uint8Array[];

    let winnersPercentList = '';
    let implementorErgoTree = '';
    let creatorErgoTree = '';
    try {
      winnersPercentList =
        (
          SConstant.from(inputExtensions![0]['0']).data as bigint[]
        ).toString() || '';
      implementorErgoTree = Buffer.from(
        (SConstant.from(inputExtensions![0]['1']).data as Uint8Array[])[0],
      ).toString('hex');
      creatorErgoTree = Buffer.from(
        (SConstant.from(inputExtensions![0]['1']).data as Uint8Array[])[1],
      ).toString('hex');
    } catch (err) {
      this.logger.error(`Error in parsing inactiveRaffle context data: ${err}`);
      return undefined;
    }

    const data = {
      boxId: box.boxId.toString(),
      txId: box.transactionId,
      raffleId: Buffer.from(R7Serialized[1]).toString('hex'),
      serialized: Buffer.from(serializeBox(box as Box).toBytes()).toString(
        'base64',
      ),
      serviceErgoTree: this.serviceErgoTree,
      implementorErgoTree: implementorErgoTree,
      creatorErgoTree: creatorErgoTree,
      winnersPercent: Number(R4Serialized[0]),
      serviceFeePercent: Number(R4Serialized[1]),
      implementerFeePercent: Number(R4Serialized[2]),
      ticketPrice: R4Serialized[3],
      goal: R4Serialized[4],
      deadline: Number(R4Serialized[5]),
      winnersPercentList: winnersPercentList,
      txFee: R4Serialized[6],
    };

    return data;
  };
}
