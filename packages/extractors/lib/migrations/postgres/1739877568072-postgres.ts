import { MigrationInterface, QueryRunner } from 'typeorm';

export class Postgres1739877568072 implements MigrationInterface {
  name = 'Postgres1739877568072';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "winner_prize" (
                "id" SERIAL NOT NULL,
                "boxId" character varying NOT NULL,
                "block" character varying NOT NULL,
                "height" integer NOT NULL,
                "spendBlock" character varying,
                "spendHeight" integer,
                "extractor" character varying NOT NULL,
                "serialized" character varying NOT NULL,
                "txId" character varying NOT NULL,
                "raffleId" character varying NOT NULL,
                "winnerTicketIndex" character varying NOT NULL,
                "giftCount" integer NOT NULL,
                "winnerIndex" integer NOT NULL,
                "unwrappedGiftCount" integer NOT NULL,
                CONSTRAINT "UQ_29dccb0aa4aab740d46522459ca" UNIQUE ("boxId", "extractor"),
                CONSTRAINT "PK_6e7548e2df646b2ac0996f9c052" PRIMARY KEY ("id")
            )
        `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE "winner_prize"
        `);
  }
}
