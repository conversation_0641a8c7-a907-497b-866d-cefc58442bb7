import { MigrationInterface, QueryRunner } from 'typeorm';

export class Postgres1739776466062 implements MigrationInterface {
  name = 'Postgres1739776466062';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE "winner" (
                "id" SERIAL NOT NULL,
                "boxId" character varying NOT NULL,
                "block" character varying NOT NULL,
                "height" integer NOT NULL,
                "spendBlock" character varying,
                "spendHeight" integer,
                "extractor" character varying NOT NULL,
                "serialized" character varying NOT NULL,
                "txId" character varying NOT NULL,
                "raffleId" character varying NOT NULL,
                "index" integer NOT NULL,
                "rewardPercent" integer NOT NULL,
                CONSTRAINT "UQ_5b52f300111033e04538ab65c52" UNIQUE ("boxId", "extractor"),
                CONSTRAINT "PK_fa2886a53e844d01b8fc5524560" PRIMARY KEY ("id")
            )
        `);
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`
            DROP TABLE "winner"
        `);
  }
}
