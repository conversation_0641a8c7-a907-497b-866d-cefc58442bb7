{"name": "@ergo-raffle/box-lookup", "version": "0.1.0", "description": "Raffle v2 box lookup", "license": "GPL-3.0", "author": "Ergo Raffle Team", "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig.build.json", "coverage": "npm run test -- --coverage", "lint": "eslint --fix . && npm run prettify", "prettify": "prettier --write . --ignore-path ./.gitignore", "release": "npm run test -- --run && npm run build && npm publish --access public", "test": "NODE_OPTIONS='--import tsx' vitest", "test:related": "NODE_OPTIONS='--import tsx' vitest related --run", "type-check": "tsc --noEmit"}, "devDependencies": {"@types/node": "^20.11.9", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "@vitest/coverage-istanbul": "^3.1.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-vitest": "^0.5.4", "prettier": "^3.2.4", "typescript": "^5.3.3", "vitest": "^3.1.1"}, "engines": {"node": ">=20.11.0"}, "dependencies": {"@fleet-sdk/core": "^0.4.1", "@fleet-sdk/serializer": "^0.8.2", "@rosen-bridge/abstract-logger": "^2.0.1", "@rosen-bridge/tx-pot": "^1.0.3", "@rosen-clients/ergo-node": "^1.2.0", "axios": "^1.8.4", "typeorm": "^0.3.20"}}