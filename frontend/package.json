{"name": "@ergo-raffle/frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "bootstrap": "^5.3.2", "react-bootstrap": "^2.9.1", "react-helmet-async": "^1.3.0", "react-toastify": "^9.1.3", "react-qr-code": "^2.0.12", "react-copy-to-clipboard": "^5.1.0", "lodash.debounce": "^4.0.8", "date-fns": "^2.30.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/lodash.debounce": "^4.0.9", "@vitejs/plugin-react": "^4.1.1", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}}