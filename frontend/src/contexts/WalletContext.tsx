import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Ergo wallet types
interface ErgoWallet {
  isConnected: boolean;
  connect: () => Promise<boolean>;
  disconnect: () => Promise<void>;
  getBalance: () => Promise<string>;
  getChangeAddress: () => Promise<string>;
  getUnusedAddresses: () => Promise<string[]>;
  getUsedAddresses: () => Promise<string[]>;
  signTx: (tx: any) => Promise<any>;
  submitTx: (signedTx: any) => Promise<string>;
}

interface WalletState {
  isConnected: boolean;
  address: string | null;
  balance: string | null;
  isLoading: boolean;
  error: string | null;
}

interface WalletContextType {
  wallet: WalletState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => Promise<void>;
  signTransaction: (tx: any) => Promise<any>;
  submitTransaction: (signedTx: any) => Promise<string>;
  refreshBalance: () => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

interface WalletProviderProps {
  children: ReactNode;
}

export const WalletProvider: React.FC<WalletProviderProps> = ({ children }) => {
  const [wallet, setWallet] = useState<WalletState>({
    isConnected: false,
    address: null,
    balance: null,
    isLoading: false,
    error: null,
  });

  // Check if Ergo wallet is available
  const getErgoWallet = (): ErgoWallet | null => {
    if (typeof window !== 'undefined' && (window as any).ergo) {
      return (window as any).ergo;
    }
    return null;
  };

  // Connect to wallet
  const connectWallet = async () => {
    setWallet(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const ergoWallet = getErgoWallet();
      if (!ergoWallet) {
        throw new Error('Ergo wallet not found. Please install Nautilus or another Ergo wallet.');
      }

      const connected = await ergoWallet.connect();
      if (!connected) {
        throw new Error('Failed to connect to wallet');
      }

      const address = await ergoWallet.getChangeAddress();
      const balance = await ergoWallet.getBalance();

      setWallet({
        isConnected: true,
        address,
        balance,
        isLoading: false,
        error: null,
      });

      // Store connection state
      localStorage.setItem('walletConnected', 'true');
    } catch (error) {
      setWallet(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to connect wallet',
      }));
    }
  };

  // Disconnect wallet
  const disconnectWallet = async () => {
    try {
      const ergoWallet = getErgoWallet();
      if (ergoWallet) {
        await ergoWallet.disconnect();
      }

      setWallet({
        isConnected: false,
        address: null,
        balance: null,
        isLoading: false,
        error: null,
      });

      localStorage.removeItem('walletConnected');
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
    }
  };

  // Sign transaction
  const signTransaction = async (tx: any) => {
    const ergoWallet = getErgoWallet();
    if (!ergoWallet || !wallet.isConnected) {
      throw new Error('Wallet not connected');
    }

    return await ergoWallet.signTx(tx);
  };

  // Submit transaction
  const submitTransaction = async (signedTx: any) => {
    const ergoWallet = getErgoWallet();
    if (!ergoWallet || !wallet.isConnected) {
      throw new Error('Wallet not connected');
    }

    return await ergoWallet.submitTx(signedTx);
  };

  // Refresh balance
  const refreshBalance = async () => {
    if (!wallet.isConnected) return;

    try {
      const ergoWallet = getErgoWallet();
      if (ergoWallet) {
        const balance = await ergoWallet.getBalance();
        setWallet(prev => ({ ...prev, balance }));
      }
    } catch (error) {
      console.error('Error refreshing balance:', error);
    }
  };

  // Auto-connect on page load if previously connected
  useEffect(() => {
    const wasConnected = localStorage.getItem('walletConnected');
    if (wasConnected === 'true') {
      connectWallet();
    }
  }, []);

  // Listen for wallet events
  useEffect(() => {
    const handleAccountChange = () => {
      if (wallet.isConnected) {
        connectWallet(); // Reconnect to get new account info
      }
    };

    const handleDisconnect = () => {
      disconnectWallet();
    };

    if (typeof window !== 'undefined' && (window as any).ergo) {
      (window as any).ergo.on?.('accountChange', handleAccountChange);
      (window as any).ergo.on?.('disconnect', handleDisconnect);
    }

    return () => {
      if (typeof window !== 'undefined' && (window as any).ergo) {
        (window as any).ergo.off?.('accountChange', handleAccountChange);
        (window as any).ergo.off?.('disconnect', handleDisconnect);
      }
    };
  }, [wallet.isConnected]);

  const contextValue: WalletContextType = {
    wallet,
    connectWallet,
    disconnectWallet,
    signTransaction,
    submitTransaction,
    refreshBalance,
  };

  return (
    <WalletContext.Provider value={contextValue}>
      {children}
    </WalletContext.Provider>
  );
};

export const useWallet = (): WalletContextType => {
  const context = useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};
