import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Ergo wallet types
interface ErgoWallet {
  isConnected?: () => Promise<boolean>;
  connect: () => Promise<boolean>;
  disconnect?: () => Promise<void>;
  getBalance: () => Promise<string>;
  getChangeAddress: () => Promise<string>;
  getUnusedAddresses: () => Promise<string[]>;
  getUsedAddresses: () => Promise<string[]>;
  signTx: (tx: any) => Promise<any>;
  submitTx: (signedTx: any) => Promise<string>;
}

interface WalletState {
  isConnected: boolean;
  address: string | null;
  balance: string | null;
  isLoading: boolean;
  error: string | null;
}

interface WalletContextType {
  wallet: WalletState;
  connectWallet: () => Promise<void>;
  disconnectWallet: () => Promise<void>;
  signTransaction: (tx: any) => Promise<any>;
  submitTransaction: (signedTx: any) => Promise<string>;
  refreshBalance: () => Promise<void>;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

interface WalletProviderProps {
  children: ReactNode;
}

export const WalletProvider: React.FC<WalletProviderProps> = ({ children }) => {
  const [wallet, setWallet] = useState<WalletState>({
    isConnected: false,
    address: null,
    balance: null,
    isLoading: false,
    error: null,
  });

  // Check if Ergo wallet is available
  const getErgoWallet = (): ErgoWallet | null => {
    if (typeof window !== 'undefined') {
      // Check for different wallet types
      if ((window as any).ergo) {
        return (window as any).ergo;
      }
      // Check for Nautilus wallet specifically
      if ((window as any).ergoConnector?.nautilus) {
        return (window as any).ergoConnector.nautilus;
      }
      // Check for other wallet connectors
      if ((window as any).ergoConnector) {
        const connectors = (window as any).ergoConnector;
        const availableWallets = Object.keys(connectors);
        if (availableWallets.length > 0) {
          return connectors[availableWallets[0]];
        }
      }
    }
    return null;
  };

  // Check if wallet is already connected
  const checkWalletConnection = async () => {
    try {
      const ergoWallet = getErgoWallet();
      if (!ergoWallet) {
        return false;
      }

      // Check if wallet has isConnected method
      if (typeof ergoWallet.isConnected === 'function') {
        const connected = await ergoWallet.isConnected();
        if (connected) {
          // Get wallet information
          const address = await ergoWallet.getChangeAddress();
          const balance = await ergoWallet.getBalance();

          setWallet({
            isConnected: true,
            address,
            balance,
            isLoading: false,
            error: null,
          });

          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn('Error checking wallet connection:', error);
      return false;
    }
  };

  // Connect to wallet
  const connectWallet = async () => {
    setWallet(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const ergoWallet = getErgoWallet();
      if (!ergoWallet) {
        throw new Error('Ergo wallet not found. Please install Nautilus wallet from https://github.com/capt-nemo429/nautilus-wallet');
      }

      // Check if already connected first
      const alreadyConnected = await checkWalletConnection();
      if (alreadyConnected) {
        console.log('Wallet was already connected');
        return;
      }

      // Check if wallet has connect method
      if (typeof ergoWallet.connect !== 'function') {
        throw new Error('Wallet does not support connection. Please update your wallet.');
      }

      console.log('Attempting to connect to wallet...');
      const connected = await ergoWallet.connect();
      if (!connected) {
        throw new Error('User rejected wallet connection');
      }

      // Get wallet information
      let address: string;
      let balance: string;

      try {
        address = await ergoWallet.getChangeAddress();
      } catch (error) {
        console.warn('Failed to get change address, trying unused addresses:', error);
        const addresses = await ergoWallet.getUnusedAddresses();
        address = addresses[0] || 'Unknown';
      }

      try {
        balance = await ergoWallet.getBalance();
      } catch (error) {
        console.warn('Failed to get balance:', error);
        balance = '0';
      }

      setWallet({
        isConnected: true,
        address,
        balance,
        isLoading: false,
        error: null,
      });

      // Store connection state
      localStorage.setItem('walletConnected', 'true');
      localStorage.setItem('walletAddress', address);
      console.log('Wallet connected successfully:', { address, balance });
    } catch (error) {
      console.error('Wallet connection error:', error);
      localStorage.removeItem('walletConnected');
      localStorage.removeItem('walletAddress');
      setWallet(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to connect wallet',
      }));
    }
  };

  // Disconnect wallet
  const disconnectWallet = async () => {
    try {
      const ergoWallet = getErgoWallet();
      if (ergoWallet && typeof ergoWallet.disconnect === 'function') {
        await ergoWallet.disconnect();
      }

      setWallet({
        isConnected: false,
        address: null,
        balance: null,
        isLoading: false,
        error: null,
      });

      localStorage.removeItem('walletConnected');
      localStorage.removeItem('walletAddress');
      console.log('Wallet disconnected successfully');
    } catch (error) {
      console.error('Error disconnecting wallet:', error);
      // Still update state even if disconnect fails
      setWallet({
        isConnected: false,
        address: null,
        balance: null,
        isLoading: false,
        error: null,
      });
      localStorage.removeItem('walletConnected');
      localStorage.removeItem('walletAddress');
    }
  };

  // Sign transaction
  const signTransaction = async (tx: any) => {
    const ergoWallet = getErgoWallet();
    if (!ergoWallet || !wallet.isConnected) {
      throw new Error('Wallet not connected');
    }

    return await ergoWallet.signTx(tx);
  };

  // Submit transaction
  const submitTransaction = async (signedTx: any) => {
    const ergoWallet = getErgoWallet();
    if (!ergoWallet || !wallet.isConnected) {
      throw new Error('Wallet not connected');
    }

    return await ergoWallet.submitTx(signedTx);
  };

  // Refresh balance
  const refreshBalance = async () => {
    if (!wallet.isConnected) return;

    try {
      const ergoWallet = getErgoWallet();
      if (ergoWallet) {
        const balance = await ergoWallet.getBalance();
        setWallet(prev => ({ ...prev, balance }));
      }
    } catch (error) {
      console.error('Error refreshing balance:', error);
    }
  };

  // Auto-connect on page load if previously connected
  useEffect(() => {
    const wasConnected = localStorage.getItem('walletConnected');
    const savedAddress = localStorage.getItem('walletAddress');

    if (wasConnected === 'true' && savedAddress) {
      console.log('Attempting to restore wallet connection...');
      checkWalletConnection().then(connected => {
        if (!connected) {
          console.log('Could not restore connection, clearing saved state');
          localStorage.removeItem('walletConnected');
          localStorage.removeItem('walletAddress');
        }
      });
    }
  }, []);

  // Listen for wallet events
  useEffect(() => {
    const handleAccountChange = () => {
      console.log('Wallet account changed');
      if (wallet.isConnected) {
        checkWalletConnection(); // Check and update connection
      }
    };

    const handleDisconnect = () => {
      console.log('Wallet disconnected externally');
      disconnectWallet();
    };

    // Listen to events from different wallet types
    const ergoWallet = getErgoWallet();
    if (ergoWallet) {
      // Try to add event listeners if supported
      try {
        if (typeof (ergoWallet as any).on === 'function') {
          (ergoWallet as any).on('accountChange', handleAccountChange);
          (ergoWallet as any).on('disconnect', handleDisconnect);
        }
      } catch (error) {
        console.warn('Could not add wallet event listeners:', error);
      }
    }

    return () => {
      // Cleanup event listeners
      if (ergoWallet) {
        try {
          if (typeof (ergoWallet as any).off === 'function') {
            (ergoWallet as any).off('accountChange', handleAccountChange);
            (ergoWallet as any).off('disconnect', handleDisconnect);
          }
        } catch (error) {
          console.warn('Could not remove wallet event listeners:', error);
        }
      }
    };
  }, [wallet.isConnected]);

  // Periodic connection check
  useEffect(() => {
    if (!wallet.isConnected) return;

    const checkInterval = setInterval(async () => {
      try {
        const ergoWallet = getErgoWallet();
        if (!ergoWallet) {
          console.log('Wallet no longer available, disconnecting');
          disconnectWallet();
          return;
        }

        // Try to verify connection is still active
        if (typeof ergoWallet.isConnected === 'function') {
          const connected = await ergoWallet.isConnected();
          if (!connected) {
            console.log('Wallet connection lost, disconnecting');
            disconnectWallet();
          }
        }
      } catch (error) {
        console.warn('Error during periodic connection check:', error);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(checkInterval);
  }, [wallet.isConnected]);

  const contextValue: WalletContextType = {
    wallet,
    connectWallet,
    disconnectWallet,
    signTransaction,
    submitTransaction,
    refreshBalance,
  };

  return (
    <WalletContext.Provider value={contextValue}>
      {children}
    </WalletContext.Provider>
  );
};

export const useWallet = (): WalletContextType => {
  const context = useContext(WalletContext);
  if (!context) {
    throw new Error('useWallet must be used within a WalletProvider');
  }
  return context;
};
