import { useQuery, UseQueryResult } from 'react-query';
import { raffleApi } from '../services/api';
import { RaffleResponse, TicketResponse, GiftResponse, WinnerResponse, PaginatedResponse } from '../types/api';

// Hook for fetching all raffles
export const useRaffles = (page = 1, limit = 10): UseQueryResult<PaginatedResponse<RaffleResponse>, Error> => {
  return useQuery(
    ['raffles', page, limit],
    () => raffleApi.getRaffles(page, limit),
    {
      staleTime: 30000, // 30 seconds
      cacheTime: 300000, // 5 minutes
      refetchOnWindowFocus: false,
    }
  );
};

// Hook for fetching a single raffle
export const useRaffle = (raffleId: string): UseQueryResult<RaffleResponse, Error> => {
  return useQuery(
    ['raffle', raffleId],
    () => raffleApi.getRaffle(raffleId),
    {
      enabled: !!raffleId,
      staleTime: 30000,
      cacheTime: 300000,
      refetchOnWindowFocus: false,
    }
  );
};

// Hook for fetching raffle tickets
export const useRaffleTickets = (raffleId: string): UseQueryResult<TicketResponse[], Error> => {
  return useQuery(
    ['raffleTickets', raffleId],
    () => raffleApi.getRaffleTickets(raffleId),
    {
      enabled: !!raffleId,
      staleTime: 30000,
      cacheTime: 300000,
      refetchOnWindowFocus: false,
    }
  );
};

// Hook for fetching raffle gifts
export const useRaffleGifts = (raffleId: string): UseQueryResult<GiftResponse[], Error> => {
  return useQuery(
    ['raffleGifts', raffleId],
    () => raffleApi.getRaffleGifts(raffleId),
    {
      enabled: !!raffleId,
      staleTime: 30000,
      cacheTime: 300000,
      refetchOnWindowFocus: false,
    }
  );
};

// Hook for fetching raffle winners
export const useRaffleWinners = (raffleId: string): UseQueryResult<WinnerResponse[], Error> => {
  return useQuery(
    ['raffleWinners', raffleId],
    () => raffleApi.getRaffleWinners(raffleId),
    {
      enabled: !!raffleId,
      staleTime: 30000,
      cacheTime: 300000,
      refetchOnWindowFocus: false,
    }
  );
};
