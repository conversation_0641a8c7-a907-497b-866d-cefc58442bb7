import { format, formatDistanceToNow, isAfter } from 'date-fns';

// Format ERG amounts (assuming 9 decimal places)
export const formatErg = (amount: string | number): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  const ergAmount = numAmount / 1_000_000_000; // Convert nanoERG to ERG
  
  if (ergAmount >= 1_000_000) {
    return `${(ergAmount / 1_000_000).toFixed(2)}M ERG`;
  } else if (ergAmount >= 1_000) {
    return `${(ergAmount / 1_000).toFixed(2)}K ERG`;
  } else if (ergAmount >= 1) {
    return `${ergAmount.toFixed(2)} ERG`;
  } else {
    return `${ergAmount.toFixed(6)} ERG`;
  }
};

// Format token amounts
export const formatTokenAmount = (amount: string | number, decimals = 0): string => {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  const tokenAmount = decimals > 0 ? numAmount / Math.pow(10, decimals) : numAmount;
  
  if (tokenAmount >= 1_000_000) {
    return `${(tokenAmount / 1_000_000).toFixed(2)}M`;
  } else if (tokenAmount >= 1_000) {
    return `${(tokenAmount / 1_000).toFixed(2)}K`;
  } else {
    return tokenAmount.toLocaleString();
  }
};

// Format percentage
export const formatPercentage = (percent: number): string => {
  return `${percent.toFixed(1)}%`;
};

// Format date
export const formatDate = (timestamp: number | string): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp * 1000);
  return format(date, 'MMM dd, yyyy HH:mm');
};

// Format relative time
export const formatRelativeTime = (timestamp: number | string): string => {
  const date = typeof timestamp === 'string' ? new Date(timestamp) : new Date(timestamp * 1000);
  return formatDistanceToNow(date, { addSuffix: true });
};

// Check if deadline has passed
export const isDeadlinePassed = (deadline: number): boolean => {
  const deadlineDate = new Date(deadline * 1000);
  return isAfter(new Date(), deadlineDate);
};

// Format deadline status
export const formatDeadlineStatus = (deadline: number): { text: string; variant: string } => {
  const deadlineDate = new Date(deadline * 1000);
  const now = new Date();
  
  if (isAfter(now, deadlineDate)) {
    return { text: 'Ended', variant: 'danger' };
  }
  
  const timeLeft = formatDistanceToNow(deadlineDate);
  return { text: `Ends in ${timeLeft}`, variant: 'success' };
};

// Truncate address for display
export const truncateAddress = (address: string, startChars = 6, endChars = 4): string => {
  if (address.length <= startChars + endChars) {
    return address;
  }
  return `${address.slice(0, startChars)}...${address.slice(-endChars)}`;
};

// Format raffle status
export const formatRaffleStatus = (status: string): { text: string; variant: string } => {
  switch (status) {
    case 'active':
      return { text: 'Active', variant: 'success' };
    case 'success':
      return { text: 'Completed', variant: 'primary' };
    case 'failed':
      return { text: 'Failed', variant: 'danger' };
    case 'inactive':
      return { text: 'Inactive', variant: 'secondary' };
    default:
      return { text: 'Unknown', variant: 'secondary' };
  }
};
