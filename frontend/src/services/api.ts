import axios from 'axios';
import { 
  RaffleResponse, 
  TicketResponse, 
  GiftResponse, 
  WinnerResponse, 
  ApiResponse, 
  PaginatedResponse 
} from '../types/api';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const raffleApi = {
  // Get all raffles with pagination
  getRaffles: async (page = 1, limit = 10): Promise<PaginatedResponse<RaffleResponse>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<RaffleResponse>>>(
      `/raffles?page=${page}&limit=${limit}`
    );
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch raffles');
    }
    
    return response.data.data;
  },

  // Get raffle by ID
  getRaffle: async (raffleId: string): Promise<RaffleResponse> => {
    const response = await api.get<ApiResponse<RaffleResponse>>(`/raffles/${raffleId}`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch raffle');
    }
    
    return response.data.data;
  },

  // Get raffle tickets
  getRaffleTickets: async (raffleId: string): Promise<TicketResponse[]> => {
    const response = await api.get<ApiResponse<TicketResponse[]>>(`/raffles/${raffleId}/tickets`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch tickets');
    }
    
    return response.data.data;
  },

  // Get raffle gifts
  getRaffleGifts: async (raffleId: string): Promise<GiftResponse[]> => {
    const response = await api.get<ApiResponse<GiftResponse[]>>(`/raffles/${raffleId}/gifts`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch gifts');
    }
    
    return response.data.data;
  },

  // Get raffle winners
  getRaffleWinners: async (raffleId: string): Promise<WinnerResponse[]> => {
    const response = await api.get<ApiResponse<WinnerResponse[]>>(`/raffles/${raffleId}/winners`);
    
    if (!response.data.success || !response.data.data) {
      throw new Error(response.data.error || 'Failed to fetch winners');
    }
    
    return response.data.data;
  },
};

export default api;
