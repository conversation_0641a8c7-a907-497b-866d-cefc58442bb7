import { useEffect, useRef, useState } from 'react';
import { useQueryClient } from 'react-query';

interface WebSocketMessage {
  type: 'raffle_update' | 'ticket_purchased' | 'gift_added' | 'raffle_ended' | 'winner_selected';
  data: any;
  raffleId?: string;
}

class WebSocketService {
  private static instance: WebSocketService;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: Map<string, Set<(data: any) => void>> = new Map();

  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  connect(url: string = 'ws://localhost:3001/ws') {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      this.ws = new WebSocket(url);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.attemptReconnect(url);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      this.attemptReconnect(url);
    }
  }

  private attemptReconnect(url: string) {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
      
      setTimeout(() => {
        this.connect(url);
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  private handleMessage(message: WebSocketMessage) {
    const { type, data, raffleId } = message;
    
    // Notify specific listeners
    const typeListeners = this.listeners.get(type);
    if (typeListeners) {
      typeListeners.forEach(listener => listener(data));
    }

    // Notify raffle-specific listeners
    if (raffleId) {
      const raffleListeners = this.listeners.get(`raffle:${raffleId}`);
      if (raffleListeners) {
        raffleListeners.forEach(listener => listener(data));
      }
    }
  }

  subscribe(eventType: string, callback: (data: any) => void) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType)!.add(callback);

    // Return unsubscribe function
    return () => {
      const listeners = this.listeners.get(eventType);
      if (listeners) {
        listeners.delete(callback);
        if (listeners.size === 0) {
          this.listeners.delete(eventType);
        }
      }
    };
  }

  subscribeToRaffle(raffleId: string, callback: (data: any) => void) {
    return this.subscribe(`raffle:${raffleId}`, callback);
  }

  send(message: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners.clear();
  }
}

// React hook for using WebSocket
export const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false);
  const wsService = useRef(WebSocketService.getInstance());
  const queryClient = useQueryClient();

  useEffect(() => {
    const service = wsService.current;
    
    // Connect to WebSocket
    service.connect();

    // Subscribe to connection status changes
    const handleOpen = () => setIsConnected(true);
    const handleClose = () => setIsConnected(false);

    // Note: In a real implementation, you'd need to expose these events
    // from the WebSocketService class
    
    return () => {
      // Don't disconnect here as other components might be using it
      // service.disconnect();
    };
  }, []);

  const subscribeToRaffleUpdates = (raffleId: string) => {
    const service = wsService.current;
    
    return service.subscribeToRaffle(raffleId, (data) => {
      // Invalidate relevant queries to trigger refetch
      queryClient.invalidateQueries(['raffle', raffleId]);
      queryClient.invalidateQueries(['raffleTickets', raffleId]);
      queryClient.invalidateQueries(['raffleGifts', raffleId]);
      queryClient.invalidateQueries(['raffleWinners', raffleId]);
    });
  };

  const subscribeToGlobalUpdates = () => {
    const service = wsService.current;
    
    const unsubscribeTicket = service.subscribe('ticket_purchased', () => {
      queryClient.invalidateQueries(['raffles']);
    });

    const unsubscribeGift = service.subscribe('gift_added', () => {
      queryClient.invalidateQueries(['raffles']);
    });

    const unsubscribeRaffleEnd = service.subscribe('raffle_ended', () => {
      queryClient.invalidateQueries(['raffles']);
    });

    return () => {
      unsubscribeTicket();
      unsubscribeGift();
      unsubscribeRaffleEnd();
    };
  };

  return {
    isConnected,
    subscribeToRaffleUpdates,
    subscribeToGlobalUpdates,
    send: (message: any) => wsService.current.send(message),
  };
};

// Hook for raffle-specific real-time updates
export const useRaffleRealTime = (raffleId: string) => {
  const { subscribeToRaffleUpdates } = useWebSocket();
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!raffleId) return;

    const unsubscribe = subscribeToRaffleUpdates(raffleId);
    setLastUpdate(new Date());

    return unsubscribe;
  }, [raffleId, subscribeToRaffleUpdates]);

  return { lastUpdate };
};

export default WebSocketService;
