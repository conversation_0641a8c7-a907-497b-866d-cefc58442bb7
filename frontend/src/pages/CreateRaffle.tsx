import React, { useState } from 'react';
import { Container, Row, Col, Card, Form, Button, Alert } from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import { useTransactionService } from '../services/transactionService';
import { toast } from 'react-toastify';

const CreateRaffle: React.FC = () => {
  const navigate = useNavigate();
  const { createRaffle, isConnected } = useTransactionService();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    ticketPrice: '',
    goal: '',
    deadline: '',
    winnersCount: 1,
    winnersPercentList: [100],
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleWinnersCountChange = (count: number) => {
    // Start with equal distribution that totals 80% (leaving 20% for creator)
    const basePercent = Math.floor(80 / count);
    const newPercentList = Array(count).fill(basePercent);

    // Distribute any remainder to the first winners
    const remainder = 80 - (basePercent * count);
    for (let i = 0; i < remainder; i++) {
      newPercentList[i] += 1;
    }

    setFormData(prev => ({
      ...prev,
      winnersCount: count,
      winnersPercentList: newPercentList
    }));
  };

  const handlePercentChange = (index: number, percent: number) => {
    const newPercentList = [...formData.winnersPercentList];
    newPercentList[index] = percent;
    setFormData(prev => ({
      ...prev,
      winnersPercentList: newPercentList
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Check wallet connection
      if (!isConnected) {
        throw new Error('Please connect your wallet to create a raffle');
      }

      // Validate form
      if (!formData.name.trim()) {
        throw new Error('Raffle name is required');
      }
      if (!formData.description.trim()) {
        throw new Error('Description is required');
      }
      if (!formData.ticketPrice || parseFloat(formData.ticketPrice) <= 0) {
        throw new Error('Valid ticket price is required');
      }
      if (!formData.goal || parseFloat(formData.goal) <= 0) {
        throw new Error('Valid goal amount is required');
      }
      if (!formData.deadline) {
        throw new Error('Deadline is required');
      }

      const totalPercent = formData.winnersPercentList.reduce((sum, p) => sum + p, 0);
      if (totalPercent > 100) {
        throw new Error('Winner percentages cannot exceed 100%');
      }
      if (totalPercent < 1) {
        throw new Error('Winner percentages must be at least 1%');
      }

      // Convert deadline to timestamp
      const deadlineTimestamp = Math.floor(new Date(formData.deadline).getTime() / 1000);

      // Prepare raffle data for blockchain transaction
      const raffleData = {
        name: formData.name,
        description: formData.description,
        ticketPrice: (parseFloat(formData.ticketPrice) * 1_000_000_000).toString(), // Convert to nanoERG
        goal: (parseFloat(formData.goal) * 1_000_000_000).toString(), // Convert to nanoERG
        deadline: deadlineTimestamp,
        winnersCount: formData.winnersCount,
        winnersPercentList: formData.winnersPercentList,
        pictures: [], // Would be populated with uploaded images
      };

      // Create raffle transaction
      toast.info('Creating raffle transaction...');
      const txId = await createRaffle(raffleData);

      toast.success(`Raffle created successfully! Transaction ID: ${txId}`);

      // Navigate to home page
      navigate('/');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create raffle';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container>
      <Row className="justify-content-center">
        <Col lg={8}>
          <Card>
            <Card.Header>
              <h2 className="mb-0">Create New Raffle</h2>
            </Card.Header>
            <Card.Body>
              {error && (
                <Alert variant="danger" className="mb-4">
                  {error}
                </Alert>
              )}

              <Form onSubmit={handleSubmit}>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Raffle Name *</Form.Label>
                      <Form.Control
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="Enter raffle name"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Deadline *</Form.Label>
                      <Form.Control
                        type="datetime-local"
                        name="deadline"
                        value={formData.deadline}
                        onChange={handleInputChange}
                        min={new Date().toISOString().slice(0, 16)}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Description *</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={4}
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Describe your raffle..."
                    required
                  />
                </Form.Group>

                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Ticket Price (ERG) *</Form.Label>
                      <Form.Control
                        type="number"
                        step="0.001"
                        min="0.001"
                        name="ticketPrice"
                        value={formData.ticketPrice}
                        onChange={handleInputChange}
                        placeholder="0.1"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Goal Amount (ERG) *</Form.Label>
                      <Form.Control
                        type="number"
                        step="0.001"
                        min="0.001"
                        name="goal"
                        value={formData.goal}
                        onChange={handleInputChange}
                        placeholder="100"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Form.Group className="mb-3">
                  <Form.Label>Number of Winners</Form.Label>
                  <Form.Select
                    value={formData.winnersCount}
                    onChange={(e) => handleWinnersCountChange(parseInt(e.target.value))}
                  >
                    {[1, 2, 3, 4, 5].map(num => (
                      <option key={num} value={num}>{num} Winner{num !== 1 ? 's' : ''}</option>
                    ))}
                  </Form.Select>
                </Form.Group>

                {formData.winnersCount > 1 && (
                  <Card className="mb-3">
                    <Card.Header>
                      <h6 className="mb-0">Prize Distribution</h6>
                      <small className="text-muted">
                        Configure how the prize pool is distributed among winners.
                        Any remaining percentage goes to the raffle creator.
                      </small>
                    </Card.Header>
                    <Card.Body>
                      {formData.winnersPercentList.map((percent, index) => (
                        <Row key={index} className="mb-2">
                          <Col sm={4}>
                            <Form.Label>Winner #{index + 1}</Form.Label>
                          </Col>
                          <Col sm={8}>
                            <Form.Control
                              type="number"
                              min="1"
                              max="100"
                              value={percent}
                              onChange={(e) => handlePercentChange(index, parseInt(e.target.value) || 0)}
                              placeholder="Percentage"
                            />
                          </Col>
                        </Row>
                      ))}
                      <small className="text-muted">
                        Total: {formData.winnersPercentList.reduce((sum, p) => sum + p, 0)}%
                        (remaining {100 - formData.winnersPercentList.reduce((sum, p) => sum + p, 0)}% goes to creator)
                      </small>
                    </Card.Body>
                  </Card>
                )}

                <div className="d-grid gap-2 d-md-flex justify-content-md-end">
                  <Button 
                    variant="secondary" 
                    onClick={() => navigate('/')}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit" 
                    variant="primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'Creating...' : 'Create Raffle'}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default CreateRaffle;
