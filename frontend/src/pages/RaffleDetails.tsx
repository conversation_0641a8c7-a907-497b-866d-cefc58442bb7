import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  Container,
  Row,
  Col,
  Card,
  Badge,
  Button,
  Tabs,
  Tab,
  ProgressBar,
  Table,
  Modal,
  Form
} from 'react-bootstrap';
import { 
  useRaffle, 
  useRaffleTickets, 
  useRaffleGifts, 
  useRaffleWinners 
} from '../hooks/useRaffles';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import ErrorMessage from '../components/Common/ErrorMessage';
import { 
  formatErg, 
  formatDeadlineStatus, 
  formatRaffleStatus, 
  truncateAddress,
  formatDate
} from '../utils/format';

const RaffleDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [showBuyModal, setShowBuyModal] = useState(false);
  const [showGiftModal, setShowGiftModal] = useState(false);
  const [ticketQuantity, setTicketQuantity] = useState(1);

  const { data: raffle, isLoading, error, refetch } = useRaffle(id!);
  const { data: tickets } = useRaffleTickets(id!);
  const { data: gifts } = useRaffleGifts(id!);
  const { data: winners } = useRaffleWinners(id!);

  if (isLoading) {
    return <LoadingSpinner text="Loading raffle details..." />;
  }

  if (error || !raffle) {
    return (
      <Container>
        <ErrorMessage 
          error={error || new Error('Raffle not found')} 
          onRetry={refetch} 
        />
      </Container>
    );
  }

  const deadlineStatus = formatDeadlineStatus(raffle.deadline);
  const raffleStatus = formatRaffleStatus(raffle.status);
  
  // Calculate progress
  const soldTickets = parseInt(raffle.totalSoldTickets || '0');
  const goal = parseInt(raffle.goal);
  const raised = soldTickets * parseInt(raffle.ticketPrice);
  const progress = goal > 0 ? Math.min((raised / goal) * 100, 100) : 0;

  return (
    <Container>
      <Row>
        <Col lg={8}>
          {/* Main Raffle Info */}
          <Card className="mb-4">
            <Card.Body>
              <div className="d-flex justify-content-between align-items-start mb-3">
                <h1>{raffle.name}</h1>
                <Badge bg={raffleStatus.variant} className="fs-6">
                  {raffleStatus.text}
                </Badge>
              </div>
              
              <p className="lead">{raffle.description}</p>
              
              {raffle.pictures.length > 0 && (
                <div className="mb-4">
                  <Row>
                    {raffle.pictures.map((picture, index) => (
                      <Col key={index} md={6} className="mb-3">
                        <img 
                          src={picture.content} 
                          alt={`Raffle image ${index + 1}`}
                          className="img-fluid rounded"
                          style={{ maxHeight: '300px', width: '100%', objectFit: 'cover' }}
                        />
                      </Col>
                    ))}
                  </Row>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Tabs for additional info */}
          <Tabs defaultActiveKey="tickets" className="mb-4">
            <Tab eventKey="tickets" title={`Tickets (${tickets?.length || 0})`}>
              <Card>
                <Card.Body>
                  {tickets && tickets.length > 0 ? (
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>Participant</th>
                          <th>Range</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {tickets.map((ticket) => (
                          <tr key={ticket.id}>
                            <td className="font-monospace">
                              {truncateAddress(ticket.donatorErgoTree)}
                            </td>
                            <td>
                              {ticket.rangeStart} - {ticket.rangeEnd}
                            </td>
                            <td>{formatDate(ticket.createdAt)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <p className="text-muted text-center py-4">
                      No tickets purchased yet
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            <Tab eventKey="gifts" title={`Gifts (${gifts?.length || 0})`}>
              <Card>
                <Card.Body>
                  {gifts && gifts.length > 0 ? (
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>Donor</th>
                          <th>Winner Position</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {gifts.map((gift) => (
                          <tr key={gift.id}>
                            <td className="font-monospace">
                              {truncateAddress(gift.donatorErgoTree)}
                            </td>
                            <td>Position #{gift.winnerIndex + 1}</td>
                            <td>{formatDate(gift.createdAt)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <p className="text-muted text-center py-4">
                      No gifts donated yet
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Tab>

            <Tab eventKey="winners" title={`Winners (${winners?.length || 0})`}>
              <Card>
                <Card.Body>
                  {winners && winners.length > 0 ? (
                    <Table responsive>
                      <thead>
                        <tr>
                          <th>Position</th>
                          <th>Reward %</th>
                          <th>Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {winners.map((winner) => (
                          <tr key={winner.id}>
                            <td>#{winner.index + 1}</td>
                            <td>{winner.rewardPercent}%</td>
                            <td>{formatDate(winner.createdAt)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  ) : (
                    <p className="text-muted text-center py-4">
                      Winners will be announced when the raffle ends
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Tab>
          </Tabs>
        </Col>

        <Col lg={4}>
          {/* Raffle Stats */}
          <Card className="mb-4">
            <Card.Header>
              <h5 className="mb-0">Raffle Statistics</h5>
            </Card.Header>
            <Card.Body>
              <div className="mb-3">
                <div className="d-flex justify-content-between mb-1">
                  <span>Progress</span>
                  <span>{progress.toFixed(1)}%</span>
                </div>
                <ProgressBar now={progress} variant={progress >= 100 ? 'success' : 'primary'} />
                <small className="text-muted">
                  {formatErg(raised)} raised of {formatErg(raffle.goal)} goal
                </small>
              </div>

              <Row className="mb-3">
                <Col>
                  <div className="text-center">
                    <div className="h4 mb-0">{formatErg(raffle.ticketPrice)}</div>
                    <small className="text-muted">Ticket Price</small>
                  </div>
                </Col>
                <Col>
                  <div className="text-center">
                    <div className="h4 mb-0">{raffle.totalSoldTickets || '0'}</div>
                    <small className="text-muted">Tickets Sold</small>
                  </div>
                </Col>
              </Row>

              <Row className="mb-3">
                <Col>
                  <div className="text-center">
                    <div className="h4 mb-0">{raffle.winnersCount}</div>
                    <small className="text-muted">Winners</small>
                  </div>
                </Col>
                <Col>
                  <div className="text-center">
                    <div className="h4 mb-0">{raffle.totalGifts || '0'}</div>
                    <small className="text-muted">Gifts</small>
                  </div>
                </Col>
              </Row>

              <div className="mb-3">
                <Badge bg={deadlineStatus.variant} className="w-100 py-2">
                  {deadlineStatus.text}
                </Badge>
              </div>

              {raffle.status === 'active' && (
                <div className="d-grid gap-2">
                  <Button 
                    variant="primary" 
                    size="lg"
                    onClick={() => setShowBuyModal(true)}
                  >
                    Buy Tickets
                  </Button>
                  <Button 
                    variant="outline-primary"
                    onClick={() => setShowGiftModal(true)}
                  >
                    Add Gift
                  </Button>
                </div>
              )}
            </Card.Body>
          </Card>

          {/* Creator Info */}
          <Card>
            <Card.Header>
              <h6 className="mb-0">Creator</h6>
            </Card.Header>
            <Card.Body>
              <div className="font-monospace small">
                {raffle.creatorErgoTree}
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Buy Tickets Modal */}
      <Modal show={showBuyModal} onHide={() => setShowBuyModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Buy Tickets</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Number of Tickets</Form.Label>
              <Form.Control
                type="number"
                min="1"
                value={ticketQuantity}
                onChange={(e) => setTicketQuantity(parseInt(e.target.value) || 1)}
              />
            </Form.Group>
            <div className="mb-3">
              <strong>Total Cost: {formatErg(parseInt(raffle.ticketPrice) * ticketQuantity)}</strong>
            </div>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowBuyModal(false)}>
            Cancel
          </Button>
          <Button variant="primary">
            Buy {ticketQuantity} Ticket{ticketQuantity !== 1 ? 's' : ''}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Add Gift Modal */}
      <Modal show={showGiftModal} onHide={() => setShowGiftModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Add Gift</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group className="mb-3">
              <Form.Label>Winner Position</Form.Label>
              <Form.Select>
                {Array.from({ length: raffle.winnersCount }, (_, i) => (
                  <option key={i} value={i}>
                    Position #{i + 1} ({raffle.winnersPercentList[i]}% of prize)
                  </option>
                ))}
              </Form.Select>
            </Form.Group>
            <Form.Group className="mb-3">
              <Form.Label>Gift Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                placeholder="Describe your gift..."
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowGiftModal(false)}>
            Cancel
          </Button>
          <Button variant="primary">
            Add Gift
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default RaffleDetails;
