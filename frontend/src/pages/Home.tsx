import React, { useState } from 'react';
import { Container, Row, Col, Button, ButtonGroup, Form } from 'react-bootstrap';
import { useRaffles } from '../hooks/useRaffles';
import RaffleCard from '../components/Raffle/RaffleCard';
import LoadingSpinner from '../components/Common/LoadingSpinner';
import ErrorMessage from '../components/Common/ErrorMessage';

const Home: React.FC = () => {
  const [page, setPage] = useState(1);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');
  const limit = 12;

  const { data, isLoading, error, refetch } = useRaffles(page, limit);

  const handleFilterChange = (newFilter: 'all' | 'active' | 'completed') => {
    setFilter(newFilter);
    setPage(1); // Reset to first page when filter changes
  };

  const filteredRaffles = data?.items.filter(raffle => {
    if (filter === 'all') return true;
    if (filter === 'active') return raffle.status === 'active';
    if (filter === 'completed') return raffle.status === 'success';
    return true;
  }) || [];

  if (isLoading) {
    return <LoadingSpinner text="Loading raffles..." />;
  }

  if (error) {
    return (
      <Container>
        <ErrorMessage error={error} onRetry={refetch} />
      </Container>
    );
  }

  return (
    <Container>
      {/* Hero Section */}
      <Row className="mb-5">
        <Col lg={8} className="mx-auto text-center">
          <h1 className="display-4 fw-bold mb-3">
            Welcome to Ergo Raffle V2
          </h1>
          <p className="lead mb-4">
            A decentralized raffle system on the Ergo blockchain supporting 
            multiple winners, various prize types, and dynamic gift donations.
          </p>
          <Button variant="primary" size="lg" href="/create">
            Create Your Raffle
          </Button>
        </Col>
      </Row>

      {/* Filters */}
      <Row className="mb-4">
        <Col md={6}>
          <h2>Active Raffles</h2>
          <p className="text-muted">
            Discover and participate in ongoing raffles
          </p>
        </Col>
        <Col md={6} className="d-flex justify-content-end align-items-center">
          <Form.Label className="me-3 mb-0">Filter:</Form.Label>
          <ButtonGroup>
            <Button
              variant={filter === 'all' ? 'primary' : 'outline-primary'}
              onClick={() => handleFilterChange('all')}
            >
              All
            </Button>
            <Button
              variant={filter === 'active' ? 'primary' : 'outline-primary'}
              onClick={() => handleFilterChange('active')}
            >
              Active
            </Button>
            <Button
              variant={filter === 'completed' ? 'primary' : 'outline-primary'}
              onClick={() => handleFilterChange('completed')}
            >
              Completed
            </Button>
          </ButtonGroup>
        </Col>
      </Row>

      {/* Raffles Grid */}
      {filteredRaffles.length === 0 ? (
        <Row>
          <Col className="text-center py-5">
            <h4>No raffles found</h4>
            <p className="text-muted">
              {filter === 'all' 
                ? 'There are no raffles available at the moment.' 
                : `There are no ${filter} raffles available at the moment.`
              }
            </p>
            <Button variant="primary" href="/create">
              Create the First Raffle
            </Button>
          </Col>
        </Row>
      ) : (
        <>
          <Row>
            {filteredRaffles.map((raffle) => (
              <Col key={raffle.id} lg={4} md={6} className="mb-4">
                <RaffleCard raffle={raffle} />
              </Col>
            ))}
          </Row>

          {/* Pagination */}
          {data && data.totalPages > 1 && (
            <Row>
              <Col className="d-flex justify-content-center">
                <ButtonGroup>
                  <Button
                    variant="outline-primary"
                    disabled={page === 1}
                    onClick={() => setPage(page - 1)}
                  >
                    Previous
                  </Button>
                  <Button variant="outline-primary" disabled>
                    Page {page} of {data.totalPages}
                  </Button>
                  <Button
                    variant="outline-primary"
                    disabled={page === data.totalPages}
                    onClick={() => setPage(page + 1)}
                  >
                    Next
                  </Button>
                </ButtonGroup>
              </Col>
            </Row>
          )}
        </>
      )}
    </Container>
  );
};

export default Home;
