export interface RaffleResponse {
  id: string;
  name: string;
  description: string;
  creatorErgoTree: string;
  ticketPrice: string;
  goal: string;
  deadline: number;
  winnersCount: number;
  winnersPercent: number;
  winnersPercentList: number[];
  serviceFeePercent: number;
  implementerFeePercent: number;
  status: 'inactive' | 'active' | 'success' | 'failed';
  pictures: PictureResponse[];
  totalSoldTickets?: string;
  totalGifts?: number;
  createdAt: string;
  updatedAt: string;
}

export interface PictureResponse {
  orderIndex: number;
  content: string;
}

export interface TicketResponse {
  id: string;
  raffleId: string;
  donatorErgoTree: string;
  rangeStart: string;
  rangeEnd: string;
  txId: string;
  createdAt: string;
}

export interface GiftResponse {
  id: string;
  raffleId: string;
  donatorErgoTree: string;
  winnerIndex: number;
  txId: string;
  createdAt: string;
}

export interface WinnerResponse {
  id: string;
  raffleId: string;
  index: number;
  rewardPercent: number;
  txId: string;
  createdAt: string;
}

export interface CreateRaffleRequest {
  name: string;
  description: string;
  ticketPrice: string;
  goal: string;
  deadline: number;
  winnersCount: number;
  winnersPercentList: number[];
  pictures: { orderIndex: number; content: string }[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
