import React from 'react';
import { <PERSON>, Badge, Button, Row, Col, ProgressBar } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import { RaffleResponse } from '../../types/api';
import { 
  formatErg, 
  formatDeadlineStatus, 
  formatRaffleStatus, 
  truncateAddress 
} from '../../utils/format';

interface RaffleCardProps {
  raffle: RaffleResponse;
}

const RaffleCard: React.FC<RaffleCardProps> = ({ raffle }) => {
  const deadlineStatus = formatDeadlineStatus(raffle.deadline);
  const raffleStatus = formatRaffleStatus(raffle.status);
  
  // Calculate progress (simplified - would need actual sold tickets vs goal)
  const soldTickets = parseInt(raffle.totalSoldTickets || '0');
  const goal = parseInt(raffle.goal);
  const progress = goal > 0 ? Math.min((soldTickets * parseInt(raffle.ticketPrice)) / goal * 100, 100) : 0;

  return (
    <Card className="h-100 shadow-sm">
      {raffle.pictures.length > 0 && (
        <Card.Img 
          variant="top" 
          src={raffle.pictures[0].content} 
          style={{ height: '200px', objectFit: 'cover' }}
          onError={(e) => {
            // Fallback to placeholder if image fails to load
            e.currentTarget.src = 'https://via.placeholder.com/300x200?text=Raffle+Image';
          }}
        />
      )}
      
      <Card.Body className="d-flex flex-column">
        <div className="d-flex justify-content-between align-items-start mb-2">
          <Card.Title className="h5 mb-0">{raffle.name}</Card.Title>
          <Badge bg={raffleStatus.variant}>{raffleStatus.text}</Badge>
        </div>
        
        <Card.Text className="text-muted small mb-3">
          {raffle.description.length > 100 
            ? `${raffle.description.substring(0, 100)}...` 
            : raffle.description
          }
        </Card.Text>
        
        <Row className="mb-3">
          <Col>
            <small className="text-muted">Ticket Price</small>
            <div className="fw-bold">{formatErg(raffle.ticketPrice)}</div>
          </Col>
          <Col>
            <small className="text-muted">Goal</small>
            <div className="fw-bold">{formatErg(raffle.goal)}</div>
          </Col>
        </Row>
        
        <Row className="mb-3">
          <Col>
            <small className="text-muted">Winners</small>
            <div className="fw-bold">{raffle.winnersCount}</div>
          </Col>
          <Col>
            <small className="text-muted">Tickets Sold</small>
            <div className="fw-bold">{raffle.totalSoldTickets || '0'}</div>
          </Col>
        </Row>
        
        <div className="mb-3">
          <div className="d-flex justify-content-between align-items-center mb-1">
            <small className="text-muted">Progress</small>
            <small className="text-muted">{progress.toFixed(1)}%</small>
          </div>
          <ProgressBar now={progress} variant={progress >= 100 ? 'success' : 'primary'} />
        </div>
        
        <div className="mb-3">
          <small className="text-muted">Creator</small>
          <div className="small font-monospace">
            {truncateAddress(raffle.creatorErgoTree)}
          </div>
        </div>
        
        <div className="mb-3">
          <Badge bg={deadlineStatus.variant} className="me-2">
            {deadlineStatus.text}
          </Badge>
          {raffle.totalGifts && raffle.totalGifts > 0 && (
            <Badge bg="info">
              {raffle.totalGifts} Gift{raffle.totalGifts !== 1 ? 's' : ''}
            </Badge>
          )}
        </div>
        
        <div className="mt-auto">
          <Button 
            as={Link} 
            to={`/raffle/${raffle.id}`} 
            variant="primary" 
            className="w-100"
          >
            View Details
          </Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default RaffleCard;
