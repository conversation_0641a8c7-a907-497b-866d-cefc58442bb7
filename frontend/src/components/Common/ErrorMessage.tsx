import React from 'react';
import { Al<PERSON>, Button } from 'react-bootstrap';

interface ErrorMessageProps {
  error: Error | string;
  onRetry?: () => void;
  variant?: string;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({ 
  error, 
  onRetry, 
  variant = 'danger' 
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message;

  return (
    <Alert variant={variant} className="d-flex justify-content-between align-items-center">
      <div>
        <strong>Error:</strong> {errorMessage}
      </div>
      {onRetry && (
        <Button variant={`outline-${variant}`} size="sm" onClick={onRetry}>
          Retry
        </Button>
      )}
    </Alert>
  );
};

export default ErrorMessage;
