import React from 'react';
import { Spinner } from 'react-bootstrap';

interface LoadingSpinnerProps {
  size?: 'sm' | undefined;
  variant?: string;
  text?: string;
  center?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size, 
  variant = 'primary', 
  text = 'Loading...', 
  center = true 
}) => {
  const content = (
    <div className="d-flex align-items-center">
      <Spinner animation="border" size={size} variant={variant} className="me-2" />
      <span>{text}</span>
    </div>
  );

  if (center) {
    return (
      <div className="d-flex justify-content-center align-items-center py-5">
        {content}
      </div>
    );
  }

  return content;
};

export default LoadingSpinner;
