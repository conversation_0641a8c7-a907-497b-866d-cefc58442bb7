import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Button, Modal } from 'react-bootstrap';

interface WalletDetectionProps {
  show: boolean;
  onHide: () => void;
}

const WalletDetection: React.FC<WalletDetectionProps> = ({ show, onHide }) => {
  const [walletStatus, setWalletStatus] = useState<{
    hasErgo: boolean;
    hasNautilus: boolean;
    hasConnector: boolean;
    availableWallets: string[];
  }>({
    hasErgo: false,
    hasNautilus: false,
    hasConnector: false,
    availableWallets: [],
  });

  useEffect(() => {
    const checkWallets = () => {
      const hasErgo = !!(window as any).ergo;
      const hasNautilus = !!(window as any).ergoConnector?.nautilus;
      const hasConnector = !!(window as any).ergoConnector;
      
      let availableWallets: string[] = [];
      if (hasConnector) {
        availableWallets = Object.keys((window as any).ergoConnector);
      }

      setWalletStatus({
        hasErgo,
        hasNautilus,
        hasConnector,
        availableWallets,
      });
    };

    checkWallets();
    
    // Check again after a short delay in case wallets are still loading
    const timer = setTimeout(checkWallets, 1000);
    
    return () => clearTimeout(timer);
  }, [show]);

  const hasAnyWallet = walletStatus.hasErgo || walletStatus.hasNautilus || walletStatus.hasConnector;

  return (
    <Modal show={show} onHide={onHide} centered>
      <Modal.Header closeButton>
        <Modal.Title>Wallet Connection</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        {hasAnyWallet ? (
          <div>
            <Alert variant="success">
              <h6>✅ Wallet Detected!</h6>
              <p className="mb-0">
                We found an Ergo wallet in your browser. You can now connect to participate in raffles.
              </p>
            </Alert>
            
            {walletStatus.availableWallets.length > 0 && (
              <div className="mt-3">
                <h6>Available Wallets:</h6>
                <ul className="mb-0">
                  {walletStatus.availableWallets.map(wallet => (
                    <li key={wallet} className="text-capitalize">{wallet}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ) : (
          <div>
            <Alert variant="warning">
              <h6>⚠️ No Ergo Wallet Found</h6>
              <p>
                To participate in raffles, you need an Ergo wallet browser extension.
                We recommend Nautilus wallet for the best experience.
              </p>
            </Alert>
            
            <div className="mt-3">
              <h6>How to get started:</h6>
              <ol>
                <li>Install Nautilus wallet from the Chrome Web Store or Firefox Add-ons</li>
                <li>Create or import your Ergo wallet</li>
                <li>Refresh this page and try connecting again</li>
              </ol>
            </div>
            
            <div className="mt-3">
              <h6>Supported Wallets:</h6>
              <ul>
                <li><strong>Nautilus</strong> - Recommended Ergo wallet</li>
                <li><strong>SAFEW</strong> - Simple and Fast Ergo Wallet</li>
                <li>Other Ergo-compatible wallets</li>
              </ul>
            </div>
          </div>
        )}
      </Modal.Body>
      <Modal.Footer>
        {!hasAnyWallet && (
          <Button 
            variant="primary" 
            href="https://github.com/capt-nemo429/nautilus-wallet" 
            target="_blank"
          >
            Get Nautilus Wallet
          </Button>
        )}
        <Button variant="secondary" onClick={onHide}>
          {hasAnyWallet ? 'Close' : 'I\'ll install later'}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default WalletDetection;
