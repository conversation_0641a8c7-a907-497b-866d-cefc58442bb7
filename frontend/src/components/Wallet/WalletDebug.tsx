import React, { useState, useEffect } from 'react';
import { <PERSON>, Button, Badge, Alert } from 'react-bootstrap';
import { useWallet } from '../../contexts/WalletContext';

const WalletDebug: React.FC = () => {
  const { wallet } = useWallet();
  const [walletInfo, setWalletInfo] = useState<any>(null);
  const [showDebug, setShowDebug] = useState(false);

  useEffect(() => {
    const checkWalletInfo = () => {
      const info = {
        hasWindow: typeof window !== 'undefined',
        hasErgo: !!(window as any).ergo,
        hasErgoConnector: !!(window as any).ergoConnector,
        hasNautilus: !!(window as any).ergoConnector?.nautilus,
        availableConnectors: (window as any).ergoConnector ? Object.keys((window as any).ergoConnector) : [],
        localStorage: {
          walletConnected: localStorage.getItem('walletConnected'),
          walletAddress: localStorage.getItem('walletAddress'),
        },
        walletState: wallet,
      };
      setWalletInfo(info);
    };

    checkWalletInfo();
    const interval = setInterval(checkWalletInfo, 2000);
    return () => clearInterval(interval);
  }, [wallet]);

  if (!showDebug) {
    return (
      <Button 
        variant="outline-secondary" 
        size="sm" 
        onClick={() => setShowDebug(true)}
        className="position-fixed"
        style={{ bottom: '20px', right: '20px', zIndex: 1000 }}
      >
        🔍 Debug
      </Button>
    );
  }

  return (
    <Card 
      className="position-fixed" 
      style={{ 
        bottom: '20px', 
        right: '20px', 
        width: '400px', 
        maxHeight: '500px', 
        overflow: 'auto',
        zIndex: 1000 
      }}
    >
      <Card.Header className="d-flex justify-content-between align-items-center">
        <h6 className="mb-0">Wallet Debug Info</h6>
        <Button variant="outline-secondary" size="sm" onClick={() => setShowDebug(false)}>
          ✕
        </Button>
      </Card.Header>
      <Card.Body>
        {walletInfo && (
          <div>
            <h6>Environment</h6>
            <div className="mb-3">
              <Badge bg={walletInfo.hasWindow ? 'success' : 'danger'} className="me-2">
                Window: {walletInfo.hasWindow ? 'Available' : 'Not Available'}
              </Badge>
              <Badge bg={walletInfo.hasErgo ? 'success' : 'warning'} className="me-2">
                window.ergo: {walletInfo.hasErgo ? 'Found' : 'Not Found'}
              </Badge>
              <Badge bg={walletInfo.hasErgoConnector ? 'success' : 'warning'} className="me-2">
                ergoConnector: {walletInfo.hasErgoConnector ? 'Found' : 'Not Found'}
              </Badge>
              <Badge bg={walletInfo.hasNautilus ? 'success' : 'warning'}>
                Nautilus: {walletInfo.hasNautilus ? 'Found' : 'Not Found'}
              </Badge>
            </div>

            {walletInfo.availableConnectors.length > 0 && (
              <div className="mb-3">
                <h6>Available Connectors</h6>
                {walletInfo.availableConnectors.map((connector: string) => (
                  <Badge key={connector} bg="info" className="me-2">
                    {connector}
                  </Badge>
                ))}
              </div>
            )}

            <h6>Local Storage</h6>
            <div className="mb-3">
              <small className="d-block">
                <strong>walletConnected:</strong> {walletInfo.localStorage.walletConnected || 'null'}
              </small>
              <small className="d-block">
                <strong>walletAddress:</strong> {walletInfo.localStorage.walletAddress || 'null'}
              </small>
            </div>

            <h6>Wallet State</h6>
            <div className="mb-3">
              <Badge bg={walletInfo.walletState.isConnected ? 'success' : 'secondary'} className="me-2">
                Connected: {walletInfo.walletState.isConnected ? 'Yes' : 'No'}
              </Badge>
              <Badge bg={walletInfo.walletState.isLoading ? 'warning' : 'secondary'} className="me-2">
                Loading: {walletInfo.walletState.isLoading ? 'Yes' : 'No'}
              </Badge>
              {walletInfo.walletState.error && (
                <Badge bg="danger">
                  Error: Yes
                </Badge>
              )}
            </div>

            {walletInfo.walletState.address && (
              <div className="mb-3">
                <small className="d-block">
                  <strong>Address:</strong> {walletInfo.walletState.address}
                </small>
              </div>
            )}

            {walletInfo.walletState.balance && (
              <div className="mb-3">
                <small className="d-block">
                  <strong>Balance:</strong> {walletInfo.walletState.balance}
                </small>
              </div>
            )}

            {walletInfo.walletState.error && (
              <Alert variant="danger" className="mt-3">
                <strong>Error:</strong> {walletInfo.walletState.error}
              </Alert>
            )}

            <div className="mt-3">
              <Button 
                variant="outline-primary" 
                size="sm" 
                onClick={() => {
                  console.log('Full wallet debug info:', walletInfo);
                  console.log('Window.ergo:', (window as any).ergo);
                  console.log('Window.ergoConnector:', (window as any).ergoConnector);
                }}
              >
                Log to Console
              </Button>
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default WalletDebug;
