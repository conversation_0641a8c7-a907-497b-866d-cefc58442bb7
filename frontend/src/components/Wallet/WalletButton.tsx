import React, { useState } from 'react';
import { Button, Dropdown, Spinner } from 'react-bootstrap';
import { useWallet } from '../../contexts/WalletContext';
import { formatErg, truncateAddress } from '../../utils/format';
import WalletDetection from './WalletDetection';

const WalletButton: React.FC = () => {
  const { wallet, connectWallet, disconnectWallet, refreshBalance } = useWallet();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showDetection, setShowDetection] = useState(false);

  const handleConnect = async () => {
    try {
      console.log('WalletButton: Attempting to connect wallet');
      await connectWallet();
      console.log('WalletButton: Wallet connection completed');
    } catch (error) {
      console.error('WalletButton: Connection failed:', error);
      // If connection fails, show detection modal
      setShowDetection(true);
    }
  };

  const handleDisconnect = async () => {
    console.log('WalletButton: Disconnecting wallet');
    await disconnectWallet();
    console.log('WalletButton: Wallet disconnected');
  };

  const handleRefreshBalance = async () => {
    setIsRefreshing(true);
    await refreshBalance();
    setIsRefreshing(false);
  };

  if (wallet.isLoading) {
    return (
      <Button variant="outline-light" size="sm" disabled>
        <Spinner animation="border" size="sm" className="me-2" />
        Connecting...
      </Button>
    );
  }

  // Debug: Log wallet state
  console.log('WalletButton render - wallet state:', {
    isConnected: wallet.isConnected,
    address: wallet.address,
    balance: wallet.balance,
    error: wallet.error,
    isLoading: wallet.isLoading
  });

  if (!wallet.isConnected) {
    return (
      <>
        <Button
          variant="outline-light"
          size="sm"
          onClick={handleConnect}
          disabled={wallet.isLoading}
        >
          Connect Wallet
        </Button>

        {wallet.error && (
          <Button
            variant="outline-warning"
            size="sm"
            onClick={() => setShowDetection(true)}
            className="ms-2"
          >
            Help
          </Button>
        )}

        <WalletDetection
          show={showDetection}
          onHide={() => setShowDetection(false)}
        />
      </>
    );
  }

  return (
    <Dropdown align="end">
      <Dropdown.Toggle variant="success" size="sm" id="wallet-dropdown">
        🔗 {wallet.address ? truncateAddress(wallet.address) : 'Connected'}
      </Dropdown.Toggle>

      <Dropdown.Menu>
        <Dropdown.Header>
          <div className="d-flex justify-content-between align-items-center">
            <span>Wallet Info</span>
            <Button
              variant="link"
              size="sm"
              className="p-0"
              onClick={handleRefreshBalance}
              disabled={isRefreshing}
            >
              {isRefreshing ? (
                <Spinner animation="border" size="sm" />
              ) : (
                <i className="bi bi-arrow-clockwise"></i>
              )}
            </Button>
          </div>
        </Dropdown.Header>
        
        <Dropdown.Item disabled>
          <div>
            <small className="text-muted">Address:</small>
            <div className="font-monospace small">
              {wallet.address ? truncateAddress(wallet.address, 8, 8) : 'N/A'}
            </div>
          </div>
        </Dropdown.Item>
        
        <Dropdown.Item disabled>
          <div>
            <small className="text-muted">Balance:</small>
            <div className="fw-bold">
              {wallet.balance ? formatErg(wallet.balance) : 'Loading...'}
            </div>
          </div>
        </Dropdown.Item>
        
        <Dropdown.Divider />
        
        <Dropdown.Item onClick={() => navigator.clipboard.writeText(wallet.address || '')}>
          <i className="bi bi-clipboard me-2"></i>
          Copy Address
        </Dropdown.Item>
        
        <Dropdown.Item onClick={handleRefreshBalance} disabled={isRefreshing}>
          <i className="bi bi-arrow-clockwise me-2"></i>
          Refresh Balance
        </Dropdown.Item>
        
        <Dropdown.Divider />
        
        <Dropdown.Item onClick={handleDisconnect} className="text-danger">
          <i className="bi bi-box-arrow-right me-2"></i>
          Disconnect
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
};

export default WalletButton;
