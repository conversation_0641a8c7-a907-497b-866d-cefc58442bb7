import React from 'react';
import { Navbar, Nav, Container } from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import WalletButton from '../Wallet/WalletButton';

const Header: React.FC = () => {
  const location = useLocation();

  return (
    <Navbar bg="dark" variant="dark" expand="lg" className="mb-4">
      <Container>
        <Navbar.Brand as={Link} to="/" className="fw-bold">
          🎲 Ergo Raffle V2
        </Navbar.Brand>
        
        <Navbar.Toggle aria-controls="basic-navbar-nav" />
        
        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="me-auto">
            <Nav.Link 
              as={Link} 
              to="/" 
              active={location.pathname === '/'}
            >
              Home
            </Nav.Link>
            <Nav.Link 
              as={Link} 
              to="/create" 
              active={location.pathname === '/create'}
            >
              Create Raffle
            </Nav.Link>
            <Nav.Link 
              as={Link} 
              to="/my-tickets" 
              active={location.pathname === '/my-tickets'}
            >
              My Tickets
            </Nav.Link>
            <Nav.Link 
              as={Link} 
              to="/winners" 
              active={location.pathname === '/winners'}
            >
              Winners
            </Nav.Link>
          </Nav>
          
          <Nav>
            <WalletButton />
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  );
};

export default Header;
