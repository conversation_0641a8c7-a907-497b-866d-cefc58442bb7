import React from 'react';
import { Container, <PERSON>, Col } from 'react-bootstrap';

const Footer: React.FC = () => {
  return (
    <footer className="bg-dark text-light py-4 mt-5">
      <Container>
        <Row>
          <Col md={6}>
            <h5>Ergo Raffle V2</h5>
            <p className="mb-0">
              A decentralized raffle system on the Ergo blockchain supporting 
              multiple winners, various prize types, and dynamic gift donations.
            </p>
          </Col>
          <Col md={3}>
            <h6>Links</h6>
            <ul className="list-unstyled">
              <li><a href="#" className="text-light text-decoration-none">Documentation</a></li>
              <li><a href="#" className="text-light text-decoration-none">GitHub</a></li>
              <li><a href="#" className="text-light text-decoration-none">Discord</a></li>
            </ul>
          </Col>
          <Col md={3}>
            <h6>Resources</h6>
            <ul className="list-unstyled">
              <li><a href="#" className="text-light text-decoration-none">Ergo Platform</a></li>
              <li><a href="#" className="text-light text-decoration-none">How it Works</a></li>
              <li><a href="#" className="text-light text-decoration-none">FAQ</a></li>
            </ul>
          </Col>
        </Row>
        <hr className="my-3" />
        <Row>
          <Col className="text-center">
            <small>&copy; 2024 Ergo Raffle V2. Built on Ergo blockchain.</small>
          </Col>
        </Row>
      </Container>
    </footer>
  );
};

export default Footer;
