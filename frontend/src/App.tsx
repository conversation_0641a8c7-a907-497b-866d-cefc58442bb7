import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import { HelmetProvider } from 'react-helmet-async';
import { ToastContainer } from 'react-toastify';

// Bootstrap CSS
import 'bootstrap/dist/css/bootstrap.min.css';
import 'react-toastify/dist/ReactToastify.css';
import './styles/custom.css';

// Components and Pages
import Layout from './components/Layout/Layout';
import Home from './pages/Home';
import RaffleDetails from './pages/RaffleDetails';
import CreateRaffle from './pages/CreateRaffle';
import { WalletProvider } from './contexts/WalletContext';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

const App: React.FC = () => {
  return (
    <HelmetProvider>
      <QueryClientProvider client={queryClient}>
        <WalletProvider>
          <Router>
            <div className="App">
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route index element={<Home />} />
                <Route path="raffle/:id" element={<RaffleDetails />} />
                <Route path="create" element={<CreateRaffle />} />
                <Route path="my-tickets" element={<div className="container py-5"><h2>My Tickets - Coming Soon</h2></div>} />
                <Route path="winners" element={<div className="container py-5"><h2>Winners - Coming Soon</h2></div>} />
                <Route path="*" element={
                  <div className="container py-5 text-center">
                    <h2>Page Not Found</h2>
                    <p>The page you're looking for doesn't exist.</p>
                  </div>
                } />
              </Route>
            </Routes>
            
            <ToastContainer
              position="top-right"
              autoClose={5000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              rtl={false}
              pauseOnFocusLoss
              draggable
              pauseOnHover
            />
            </div>
          </Router>
        </WalletProvider>
      </QueryClientProvider>
    </HelmetProvider>
  );
};

export default App;
